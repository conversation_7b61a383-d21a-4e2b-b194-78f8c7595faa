# 刮刮乐ESC关闭鼠标不消失问题修复总结

## 问题分析

### 根本原因
刮刮乐ESC关闭时鼠标不消失的问题是由于**双重ESC键处理机制**和**焦点管理冲突**导致的竞态条件问题。

### 具体问题
1. **双重ESC键处理**：
   - 客户端Lua处理：`client/main.lua:298-320`
   - 前端JavaScript处理：`ui/script.js:287-303`
   - 两套处理机制可能产生竞态条件

2. **焦点管理冲突**：
   - 多个地方同时请求焦点：`openScratchCard()`, `setupScratchCanvas()`, `revealAll()`, `showScratchResult()`
   - ESC关闭过程中焦点请求与`SetNuiFocus(false, false)`冲突

3. **时序问题**：
   - 硬件性能差异导致处理速度不同
   - 网络延迟影响回调响应时间
   - 异步操作（图像加载、结果处理）增加冲突概率

## 修复方案

### 1. 简化ESC键处理机制
**修改文件：** `ui/script.js`, `client/main.lua`

**前端修改：**
```javascript
// ESC键关闭 - 使用改进的焦点管理
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        // 检查当前界面状态并关闭
        if (!document.getElementById('scratch-game').classList.contains('hidden')) {
            this.closeScratchCard();
        } else if (!document.getElementById('lottery-main').classList.contains('hidden')) {
            this.closeLottery();
        }
    }
});
```

**客户端修改：**
```lua
-- ESC键关闭界面 - 简化版本，确保基本功能正常
CreateThread(function()
    while true do
        if isUIOpen then
            if IsControlJustReleased(0, 322) then -- ESC键
                -- 直接关闭界面，不再通过复杂的状态检查
                CloseLotteryShop()
            end
        end
        Wait(0)
    end
end)
```

### 2. 焦点状态管理机制
**修改文件：** `ui/script.js`

**添加焦点状态管理：**
```javascript
// 焦点状态管理
this.focusState = {
    isChanging: false,  // 是否正在改变焦点状态
    pendingRequests: [], // 待处理的焦点请求队列
    currentFocus: false  // 当前焦点状态
};
```

**焦点请求方法：**
```javascript
requestFocus(focus, reason = 'unknown') {
    // 防止焦点请求冲突的队列机制
    if (this.focusState.isChanging) {
        this.focusState.pendingRequests.push({ focus, reason, timestamp: Date.now() });
        return;
    }
    // ... 处理焦点请求
}
```

### 3. 优化刮刮乐关闭流程
**修改文件：** `ui/script.js`

**关闭时清理所有状态：**
```javascript
closeScratchCard() {
    // 清除所有待处理的焦点请求，防止关闭后还有焦点请求
    this.focusState.pendingRequests = [];
    this.focusState.isChanging = false;

    // 清除刮奖检查定时器
    if (this.scratchCheckTimeout) {
        clearTimeout(this.scratchCheckTimeout);
        this.scratchCheckTimeout = null;
    }

    // 使用改进的焦点管理确保鼠标正确消失
    this.requestFocus(false, 'closeScratchCard');

    // ... 其他清理逻辑
}
```

## 测试验证

### 测试场景
1. **刮奖过程中按ESC**：验证鼠标正确消失
2. **自动显示全部后立即按ESC**：验证无焦点冲突
3. **中奖结果显示期间按ESC**：验证正确关闭
4. **不同性能设备测试**：验证竞态条件解决

### 预期效果
- ✅ ESC键关闭刮刮乐时鼠标始终正确消失
- ✅ 无论何时按ESC都能正确关闭界面
- ✅ 不同性能设备表现一致
- ✅ 无焦点请求冲突

## 修改文件列表

1. **ui/script.js**
   - 移除前端ESC键处理
   - 添加焦点状态管理机制
   - 优化刮刮乐关闭流程
   - 替换所有直接焦点请求为管理方法

2. **client/main.lua**
   - 统一ESC键处理逻辑
   - 添加UI状态检查机制

## 技术要点

1. **竞态条件解决**：通过统一ESC键处理和焦点队列机制
2. **状态管理**：确保关闭时完全清理所有状态
3. **异步处理**：防止异步操作中的焦点请求冲突
4. **兼容性**：保持原有功能不变，只修复问题

## 注意事项

- 修改后需要重启资源生效
- 建议在不同性能设备上测试
- 如有问题可回滚到原版本
