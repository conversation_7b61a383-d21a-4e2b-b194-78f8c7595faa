-- 刮刮乐逻辑模块

-- 获取框架对象
local ESX, QBCore = nil, nil
if Config.Framework == 'ESX' then
    ESX = exports['es_extended']:getSharedObject()
elseif Config.Framework == 'QB' then
    QBCore = exports['qb-core']:GetCoreObject()
end

-- 调试打印函数 - 只在调试模式开启时打印
local function DebugPrint(message, level)
    if Config.Debug and Config.Debug.enabled then
        level = level or "^7" -- 默认白色
        print(level .. "[彩票系统-调试] " .. message)
    end
end

-- 系统消息打印函数 - 总是打印重要的系统消息
local function SystemPrint(message, level)
    level = level or "^7" -- 默认白色
    print(level .. "[彩票系统] " .. message)
end

-- 初始化ox_inventory钩子
local oxInventoryLoaded = false

-- 根据服务器ID获取玩家对象
function GetPlayerFromId(source)
    if Config.Framework == 'ESX' then
        return ESX.GetPlayerFromId(source)
    elseif Config.Framework == 'QB' then
        return QBCore.Functions.GetPlayer(source)
    end
    return nil
end

-- 检查并加载ox_inventory
CreateThread(function()
    while not oxInventoryLoaded do
        if GetResourceState('ox_inventory') == 'started' then
            DebugPrint("检测到ox_inventory，注册物品钩子")
            
            -- 注册usedItem事件监听
            AddEventHandler('ox_inventory:usedItem', function(source, itemName, slot)
                -- 检查是否是我们的刮刮乐物品
                for cardType, itemId in pairs(Config.Items.scratchItems) do
                    if itemName == itemId then
                        DebugPrint("刮刮乐物品使用: " .. itemName .. " 玩家ID: " .. source .. " 槽位: " .. slot)
                        
                        -- 获取物品元数据
                        local item = exports.ox_inventory:GetSlot(source, slot)
                        if item then
                            DebugPrint("物品数据: " .. json.encode(item))
                            OpenScratchCardFromItem(source, cardType, {metadata = item.metadata})
                        else
                            DebugPrint("无法获取物品数据")
                            OpenScratchCardFromItem(source, cardType, nil)
                        end
                        
                        break
                    end
                end
            end)
            
            oxInventoryLoaded = true
            DebugPrint("ox_inventory钩子注册完成")
        end
        
        Wait(1000) -- 每秒检查一次
    end
end)

-- 购买刮刮乐
RegisterNetEvent('lottery:buyScratchCard')
AddEventHandler('lottery:buyScratchCard', function(data)
    local source = source
    local player = GetPlayer(source)
    
    if not player then
        TriggerClientEvent('lottery:buyResult', source, {success = false, message = "玩家数据错误"})
        return
    end
    
    local cardType = data.cardType
    local cardConfig = Config.ScratchCards[cardType]
    
    if not cardConfig then
        TriggerClientEvent('lottery:buyResult', source, {success = false, message = "无效的卡片类型"})
        return
    end
    
    local playerMoney = GetPlayerMoney(source)
    local price = cardConfig.price
    
    -- 检查金钱
    if playerMoney < price then
        TriggerClientEvent('lottery:buyResult', source, {success = false, message = "余额不足"})
        return
    end
    
    -- 扣除金钱
    if not RemovePlayerMoney(source, price) then
        TriggerClientEvent('lottery:buyResult', source, {success = false, message = "扣费失败"})
        return
    end
    
    -- 检查是否为特殊刮刮乐类型（这些类型的购买金钱不进入彩票账户）
    local specialScratchCards = {
        "scratch_caizuan",    -- 5倍彩钻
        "scratch_zhongguofu", -- 中国福
        "scratch_chengfeng"   -- 乘风破浪
    }

    local isSpecialCard = false
    for _, specialType in ipairs(specialScratchCards) do
        if cardType == specialType then
            isSpecialCard = true
            break
        end
    end

    -- 只有非特殊刮刮乐的购买金额才存入彩票店账户
    if not isSpecialCard and Config.MoneyFlow and Config.MoneyFlow.scratchCard then
        local shopAccountContribution = math.floor(price * Config.MoneyFlow.scratchCard.shopAccount)
        -- 调用prizes.lua中的UpdateShopAccount函数
        exports[GetCurrentResourceName()]:UpdateShopAccount(shopAccountContribution)
        DebugPrint("^3[彩票系统] ^7" .. cardConfig.name .. "购买金额 " .. price .. " 已存入彩票店账户")
    else
        DebugPrint("^3[彩票系统] ^7" .. cardConfig.name .. "为特殊刮刮乐，购买金额 " .. price .. " 不进入彩票店账户")
    end
    
    -- 生成刮刮乐数据
    local ticketData = GenerateScratchTicketData(cardType)
    
    -- 保存购买记录 (使用异步回调)
    local playerData = {
        identifier = GetPlayerIdentifier(source),
        name = GetPlayerName(source)
    }

    SaveScratchCard(playerData, cardType, cardConfig.name, price, ticketData, function(ticketId)
        if ticketId then
            -- 如果使用物品系统，给予物品
            if Config.Items.useScratchItems then
                local itemName = Config.Items.scratchItems[cardType]
                if itemName then
                    -- 准备元数据，兼容不同框架
                    local metadata = nil

                    -- 检测是否使用ox_inventory
                    if GetResourceState('ox_inventory') == 'started' then
                        -- ox_inventory格式
                        metadata = {
                            -- 基本信息
                            ticketId = ticketId,
                            -- 显示信息
                            label = cardConfig.name .. " 刮刮乐",
                            description = "刮刮乐票据 #" .. ticketId,
                            -- 其他信息
                            card_type = cardType,
                            max_prize = cardConfig.maxPrize
                        }
                        DebugPrint("准备ox_inventory元数据: " .. json.encode(metadata))
                    else
                        -- 传统框架格式
                        metadata = {ticketId = ticketId}
                    end

                    GivePlayerItem(source, itemName, 1, metadata)
                end
            else
                -- 直接打开刮刮乐游戏
                TriggerClientEvent('lottery:openScratchGame', source, {
                    ticketId = ticketId,
                    cardType = cardType,
                    cardName = cardConfig.name,
                    ticketData = ticketData
                })
            end

            SendNotification(source, "购买" .. cardConfig.name .. "成功！", "success")
            TriggerClientEvent('lottery:buyResult', source, {success = true, ticketId = ticketId})
        else
            -- 保存失败，退款
            AddPlayerMoney(source, price)
            SendNotification(source, "保存失败，已退款", "error")
            TriggerClientEvent('lottery:buyResult', source, {success = false, message = "保存失败，已退款"})
        end
    end)
end)

-- 使用刮刮乐物品 (如果启用物品系统)
if Config.Items.useScratchItems then
    -- 如果ox_inventory可用，优先使用ox_inventory的物品使用事件
    if GetResourceState('ox_inventory') == 'started' then
        DebugPrint("检测到ox_inventory，使用ox_inventory进行物品注册")
        -- 物品已在顶部的CreateThread中注册，这里不需要重复注册
        for cardType, itemName in pairs(Config.Items.scratchItems) do
            DebugPrint("物品名称: " .. itemName .. " 对应卡片类型: " .. cardType)
        end
        -- 不要设置oxInventoryLoaded = true，让顶部的CreateThread正常完成事件注册
    else
        -- 如果ox_inventory不可用，使用传统框架注册方式
        for cardType, itemName in pairs(Config.Items.scratchItems) do
            if Config.Framework == 'ESX' then
                ESX.RegisterUsableItem(itemName, function(source)
                    OpenScratchCardFromItem(source, cardType)
                end)
            elseif Config.Framework == 'QB' then
                QBCore.Functions.CreateUseableItem(itemName, function(source, item)
                    OpenScratchCardFromItem(source, cardType, item)
                end)
            end
        end
    end
end

-- 从物品打开刮刮乐
function OpenScratchCardFromItem(source, cardType, item)
    local player = GetPlayerFromId(source)
    if not player then 
        DebugPrint("OpenScratchCardFromItem: 找不到玩家 " .. source)
        return 
    end
    
    DebugPrint("OpenScratchCardFromItem被调用: " .. cardType)
    
    -- 调试打印物品数据
    if item then
        DebugPrint("物品数据: " .. json.encode(item))
    else
        DebugPrint("物品数据为空")
    end
    
    -- 提取ticketId，兼容不同框架
    local ticketId = nil
    if item then
        if item.info and item.info.ticketId then
            -- QB Core 或 自定义格式
            ticketId = item.info.ticketId
            DebugPrint("从item.info中获取ticketId: " .. tostring(ticketId))
        elseif item.metadata and item.metadata.ticketId then
            -- ox_inventory 格式(metadata作为参数传入)
            ticketId = item.metadata.ticketId
            DebugPrint("从item.metadata中获取ticketId: " .. tostring(ticketId))
        elseif type(item) == "table" and item.metadata then
            -- 直接传递的metadata对象
            ticketId = item.metadata.ticketId
            DebugPrint("从直接传递的metadata中获取ticketId: " .. tostring(ticketId))
        end
    end
    
    -- 如果没有找到ticketId，尝试从数据库获取最新的未刮开的票据
    if not ticketId then
        DebugPrint("无法从物品获取ticketId，尝试从数据库查找最新票据")
        local playerId = GetPlayerIdentifier(source)
        local latestTicket = MySQL.Sync.fetchSingle('SELECT id FROM scratch_cards WHERE player_id = ? AND card_type = ? AND is_scratched = 0 ORDER BY id DESC LIMIT 1', {
            playerId, cardType
        })
        
        if latestTicket then
            ticketId = latestTicket.id
            DebugPrint("从数据库找到最新票据ID: " .. tostring(ticketId))
        end
    end
    
    if not ticketId then
        DebugPrint("无法获取ticketId")
        SendNotification(source, "无效的刮刮乐", "error")
        return
    end
    
    DebugPrint("玩家使用刮刮乐: " .. cardType .. ", 票据ID: " .. ticketId)
    
    -- 获取刮刮乐数据
    local ticket = MySQL.Sync.fetchSingle('SELECT * FROM scratch_cards WHERE id = ? AND player_id = ?', {
        ticketId, GetPlayerIdentifier(source)
    })
    
    if not ticket then
        DebugPrint("找不到刮刮乐数据: 票据ID=" .. ticketId .. ", 玩家ID=" .. GetPlayerIdentifier(source))
        SendNotification(source, "找不到刮刮乐数据", "error")
        return
    end
    
    if ticket.is_scratched == 1 then
        DebugPrint("此刮刮乐已经刮过了: 票据ID=" .. ticketId)
        SendNotification(source, "此刮刮乐已经刮过了", "error")
        return
    end
    
    local ticketData = json.decode(ticket.ticket_data)
    if not ticketData then
        DebugPrint("解析ticket_data失败")
        SendNotification(source, "刮刮乐数据错误", "error")
        return
    end
    
    -- 调试打印完整的票据数据
    DebugPrint("福鼠送彩票据数据: " .. json.encode(ticketData))
    
    DebugPrint("成功获取刮刮乐数据，准备打开UI")
    
    -- 成功后打开刮刮乐游戏
    TriggerClientEvent('lottery:openScratchGame', source, {
        ticketId = ticketId,
        cardType = cardType,
        cardName = ticket.card_name,
        ticketData = ticketData
    })
end

-- 关闭刮刮乐游戏
RegisterNetEvent('lottery:closeScratchCard')
AddEventHandler('lottery:closeScratchCard', function()
    local source = source
    -- 客户端关闭UI时不需要服务器响应
end)

-- 完成刮奖
RegisterNetEvent('lottery:finishScratch')
AddEventHandler('lottery:finishScratch', function(data)
    local source = source
    local ticketId = data.ticketId
    local cardType = data.cardType
    
    if not ticketId or not cardType then
        TriggerClientEvent('lottery:scratchResult', source, {success = false, message = "参数错误"})
        return
    end
    
    -- 获取刮刮乐记录
    local ticket = MySQL.Async.fetchSingle('SELECT * FROM scratch_cards WHERE id = ? AND player_id = ?', {
        ticketId, GetPlayerIdentifier(source)
    })
    
    if not ticket then
        TriggerClientEvent('lottery:scratchResult', source, {success = false, message = "找不到刮刮乐记录"})
        return
    end
    
    if ticket.is_scratched then
        TriggerClientEvent('lottery:scratchResult', source, {success = false, message = "此刮刮乐已经刮过了"})
        return
    end
    
    -- 计算中奖结果
    local ticketData = json.decode(ticket.ticket_data)
    local prizeAmount = CalculateScratchPrize(cardType, ticketData)

    -- 更新记录（包含更新后的ticketData）
    UpdateScratchResult(ticketId, prizeAmount, ticketData)

    -- 清除中奖记录和未兑奖记录缓存，确保数据实时更新
    exports[GetCurrentResourceName()]:ClearCache("winningRecords")
    exports[GetCurrentResourceName()]:ClearCache("unclaimedRecords")

    -- 如果使用物品系统，移除物品
    if Config.Items.useScratchItems then
        local itemName = Config.Items.scratchItems[cardType]
        
        if GetResourceState('ox_inventory') == 'started' then
            -- ox_inventory方式，查找具有匹配ticketId的物品
            local items = exports.ox_inventory:Search(source, 'slots', itemName)
            if items then
                for _, item in pairs(items) do
                    if item.metadata and item.metadata.ticketId == ticketId then
                        DebugPrint("找到并移除物品: " .. itemName .. " 槽位: " .. item.slot)
                        exports.ox_inventory:RemoveItem(source, itemName, 1, nil, item.slot)
                        break
                    end
                end
            end
        else
            -- 传统框架方式
            RemovePlayerItem(source, itemName, 1)
        end
    end
    
    -- 特殊处理：5倍彩钻和中国福刮刮乐给予物品奖励
    if (cardType == "scratch_caizuan" or cardType == "scratch_zhongguofu") and ticketData.rewardItems and #ticketData.rewardItems > 0 then
        -- 给予物品奖励
        local itemsGiven = {}
        local itemCounts = {}

        -- 统计物品数量
        for _, item in ipairs(ticketData.rewardItems) do
            if itemCounts[item] then
                itemCounts[item] = itemCounts[item] + 1
            else
                itemCounts[item] = 1
            end
        end

        -- 给予物品
        local allItemsGiven = true
        for item, count in pairs(itemCounts) do
            -- 解析带数量的物品格式 (例如: "money:1000")
            local itemName = item
            local itemAmount = count

            if string.find(item, ":") then
                local parts = {}
                for part in string.gmatch(item, "([^:]+)") do
                    table.insert(parts, part)
                end
                if #parts == 2 then
                    itemName = parts[1]
                    itemAmount = tonumber(parts[2]) * count -- 单个物品数量 * 获得次数
                end
            end

            local success = GivePlayerItem(source, itemName, itemAmount)
            if success then
                local displayName = GetItemDisplayName(itemName)
                if itemName == "money" then
                    table.insert(itemsGiven, displayName .. " ¥" .. itemAmount)
                else
                    table.insert(itemsGiven, displayName .. " x" .. itemAmount)
                end
                DebugPrint("成功给予玩家物品: " .. itemName .. " x" .. itemAmount)
            else
                allItemsGiven = false
                DebugPrint("给予玩家物品失败: " .. itemName .. " x" .. itemAmount)
            end
        end

        if allItemsGiven then
            -- 所有物品都给予成功
            ClaimPrize(ticketId, 'scratch')

            -- 记录交易明细
            local playerIdentifier = GetPlayerIdentifier(source)
            local playerName = GetPlayerName(source)
            local cardName = cardType == "scratch_caizuan" and "5倍彩钻" or (cardType == "scratch_zhongguofu" and "中国福" or "乘风破浪")
            exports[GetCurrentResourceName()]:LogTransaction('prize', 0, cardName .. '刮刮乐兑奖: ' .. table.concat(itemsGiven, ", "), playerIdentifier, playerName)

            DebugPrint("^2[彩票系统] ^7" .. cardName .. "刮刮乐兑奖成功，给予物品: " .. table.concat(itemsGiven, ", "))

            -- 发送结果给客户端
            TriggerClientEvent('lottery:scratchResult', source, {
                action = 'scratchResult',
                result = {
                    prizeAmount = 0, -- 彩钻刮刮乐不给金钱
                    isWinning = true,
                    itemRewards = itemsGiven,
                    cardType = cardType
                }
            })
        else
            -- 部分物品给予失败
            SendNotification(source, "兑奖失败，背包空间不足或物品系统错误", "error")
            TriggerClientEvent('lottery:scratchResult', source, {
                action = 'scratchResult',
                result = {
                    prizeAmount = 0,
                    isWinning = true,
                    paymentFailed = true,
                    message = "兑奖失败，背包空间不足或物品系统错误",
                    cardType = cardType
                }
            })
        end
        return
    end

    -- 如果中奖，从彩票店账户扣款
    if prizeAmount > 0 then
        -- 从彩票店账户扣款
        if Config.Framework == "ESX" then
            -- 使用ESX_addonaccount扣款
            TriggerEvent('esx_addonaccount:getSharedAccount', 'society_lottery', function(societyAccount)
                if societyAccount and societyAccount.money >= prizeAmount then
                    -- 账户余额足够，尝试扣款
                    local previousBalance = societyAccount.money
                    societyAccount.removeMoney(prizeAmount)
                    
                    -- 验证扣款是否成功
                    if societyAccount.money == (previousBalance - prizeAmount) then
                        -- 扣款成功，给玩家添加现金
                        AddPlayerMoney(source, prizeAmount)
                        ClaimPrize(ticketId, 'scratch')
                        
                        -- 记录交易明细
                        local playerIdentifier = GetPlayerIdentifier(source)
                        local playerName = GetPlayerName(source)
                        exports[GetCurrentResourceName()]:LogTransaction('prize', -prizeAmount, '刮刮乐兑奖', playerIdentifier, playerName)
                        
                        DebugPrint("^2[彩票系统] ^7刮刮乐兑奖成功，从彩票店账户扣除: " .. prizeAmount)
                        
                        -- 发送结果给客户端
                        TriggerClientEvent('lottery:scratchResult', source, {
                            action = 'scratchResult',
                            result = {
                                prizeAmount = prizeAmount,
                                isWinning = true
                            }
                        })
                    else
                        -- 账户扣款失败，检查是否启用职业系统
                        if Config.LotteryJob and Config.LotteryJob.enabled then
                            -- 职业系统启用，提示玩家联系彩票店工作人员
                            DebugPrint("^1[彩票系统] ^7彩票店账户扣款失败")
                            SendNotification(source, "兑奖失败，彩票店账户余额不足，请联系彩票店工作人员", "error")
                            
                            -- 发送结果给客户端，显示中奖但未领取
                            TriggerClientEvent('lottery:scratchResult', source, {
                                action = 'scratchResult',
                                result = {
                                    prizeAmount = prizeAmount,
                                    isWinning = true,
                                    paymentFailed = true,
                                    message = "兑奖失败，彩票店账户余额不足，请联系彩票店工作人员"
                                }
                            })
                        else
                            -- 职业系统未启用，直接给予奖金
                            DebugPrint("^1[彩票系统] ^7彩票店账户扣款失败，职业系统未启用，直接给予奖金")
                            AddPlayerMoney(source, prizeAmount)
                            ClaimPrize(ticketId, 'scratch')
                            
                            -- 发送结果给客户端
                            TriggerClientEvent('lottery:scratchResult', source, {
                                action = 'scratchResult',
                                result = {
                                    prizeAmount = prizeAmount,
                                    isWinning = true
                                }
                            })
                        end
                    end
                else
                    -- 账户余额不足，检查是否启用职业系统
                    if Config.LotteryJob and Config.LotteryJob.enabled then
                        -- 职业系统启用，提示玩家联系彩票店工作人员
                        DebugPrint("^1[彩票系统] ^7彩票店账户余额不足")
                        SendNotification(source, "兑奖失败，彩票店账户余额不足，请联系彩票店工作人员", "error")
                        
                        -- 发送结果给客户端，显示中奖但未领取
                        TriggerClientEvent('lottery:scratchResult', source, {
                            action = 'scratchResult',
                            result = {
                                prizeAmount = prizeAmount,
                                isWinning = true,
                                paymentFailed = true,
                                message = "兑奖失败，彩票店账户余额不足，请联系彩票店工作人员"
                            }
                        })
                    else
                        -- 职业系统未启用，直接给予奖金
                        DebugPrint("^1[彩票系统] ^7彩票店账户余额不足，职业系统未启用，直接给予奖金")
                        AddPlayerMoney(source, prizeAmount)
                        ClaimPrize(ticketId, 'scratch')
                        
                        -- 发送结果给客户端
                        TriggerClientEvent('lottery:scratchResult', source, {
                            action = 'scratchResult',
                            result = {
                                prizeAmount = prizeAmount,
                                isWinning = true
                            }
                        })
                    end
                end
            end)
        else -- Framework == "QB"
            -- 使用lottery_shop_accounts表扣款
            MySQL.Async.fetchAll('SELECT * FROM lottery_shop_accounts LIMIT 1', {}, function(result)
                if result and result[1] and result[1].balance >= prizeAmount then
                    -- 账户余额足够，从账户扣除
                    MySQL.Async.execute('UPDATE lottery_shop_accounts SET balance = balance - ?, total_payout = total_payout + ? WHERE id = 1 AND balance >= ?', 
                        {prizeAmount, prizeAmount, prizeAmount}, 
                        function(rowsChanged)
                            if rowsChanged > 0 then
                                -- 账户扣款成功，给玩家添加现金
                                AddPlayerMoney(source, prizeAmount)
                                ClaimPrize(ticketId, 'scratch')
                                
                                -- 记录交易明细
                                local playerIdentifier = GetPlayerIdentifier(source)
                                local playerName = GetPlayerName(source)
                                exports[GetCurrentResourceName()]:LogTransaction('prize', -prizeAmount, '刮刮乐兑奖', playerIdentifier, playerName)
                                
                                DebugPrint("^2[彩票系统] ^7刮刮乐兑奖成功，从彩票店账户扣除: " .. prizeAmount)
                                
                                -- 发送结果给客户端
                                TriggerClientEvent('lottery:scratchResult', source, {
                                    action = 'scratchResult',
                                    result = {
                                        prizeAmount = prizeAmount,
                                        isWinning = true
                                    }
                                })
                            else
                                -- 账户扣款失败，检查是否启用职业系统
                                if Config.LotteryJob and Config.LotteryJob.enabled then
                                    -- 职业系统启用，提示玩家联系彩票店工作人员
                                    DebugPrint("^1[彩票系统] ^7彩票店账户扣款失败")
                                    SendNotification(source, "兑奖失败，彩票店账户余额不足，请联系彩票店工作人员", "error")
                                    
                                    -- 发送结果给客户端，显示中奖但未领取
                                    TriggerClientEvent('lottery:scratchResult', source, {
                                        action = 'scratchResult',
                                        result = {
                                            prizeAmount = prizeAmount,
                                            isWinning = true,
                                            paymentFailed = true,
                                            message = "兑奖失败，彩票店账户余额不足，请联系彩票店工作人员"
                                        }
                                    })
                                else
                                    -- 职业系统未启用，直接给予奖金
                                    DebugPrint("^1[彩票系统] ^7彩票店账户扣款失败，职业系统未启用，直接给予奖金")
                                    AddPlayerMoney(source, prizeAmount)
                                    ClaimPrize(ticketId, 'scratch')
                                    
                                    -- 发送结果给客户端
                                    TriggerClientEvent('lottery:scratchResult', source, {
                                        action = 'scratchResult',
                                        result = {
                                            prizeAmount = prizeAmount,
                                            isWinning = true
                                        }
                                    })
                                end
                            end
                        end
                    )
                else
                    -- 账户余额不足，检查是否启用职业系统
                    if Config.LotteryJob and Config.LotteryJob.enabled then
                        -- 职业系统启用，提示玩家联系彩票店工作人员
                        DebugPrint("^1[彩票系统] ^7彩票店账户余额不足")
                        SendNotification(source, "兑奖失败，彩票店账户余额不足，请联系彩票店工作人员", "error")
                        
                        -- 发送结果给客户端，显示中奖但未领取
                        TriggerClientEvent('lottery:scratchResult', source, {
                            action = 'scratchResult',
                            result = {
                                prizeAmount = prizeAmount,
                                isWinning = true,
                                paymentFailed = true,
                                message = "兑奖失败，彩票店账户余额不足，请联系彩票店工作人员"
                            }
                        })
                    else
                        -- 职业系统未启用，直接给予奖金
                        DebugPrint("^1[彩票系统] ^7彩票店账户余额不足，职业系统未启用，直接给予奖金")
                        AddPlayerMoney(source, prizeAmount)
                        ClaimPrize(ticketId, 'scratch')
                        
                        -- 发送结果给客户端
                        TriggerClientEvent('lottery:scratchResult', source, {
                            action = 'scratchResult',
                            result = {
                                prizeAmount = prizeAmount,
                                isWinning = true
                            }
                        })
                    end
                end
            end)
        end
    else
        -- 没有中奖，直接发送结果
        TriggerClientEvent('lottery:scratchResult', source, {
            action = 'scratchResult',
            result = {
                prizeAmount = prizeAmount,
                isWinning = false
            }
        })
    end
end)

-- 自动兑奖当全部显示时
RegisterNetEvent('lottery:autoClaimOnReveal')
AddEventHandler('lottery:autoClaimOnReveal', function(data)
    local source = source
    local ticketId = data.ticketId
    local cardType = data.cardType
    
    if not ticketId or not cardType then
        DebugPrint("自动兑奖参数错误，ticketId或cardType为空")
        return
    end
    
    DebugPrint("玩家 " .. source .. " 触发自动兑奖: " .. cardType .. ", 票据ID: " .. ticketId)
    
    -- 获取刮刮乐记录
    local ticket = MySQL.Sync.fetchSingle('SELECT * FROM scratch_cards WHERE id = ? AND player_id = ?', {
        ticketId, GetPlayerIdentifier(source)
    })
    
    if not ticket then
        DebugPrint("找不到刮刮乐记录: 票据ID=" .. ticketId)
        return
    end
    
    if ticket.is_scratched == 1 then
        DebugPrint("此刮刮乐已经刮过了: 票据ID=" .. ticketId)
        return
    end
    
    -- 计算中奖结果
    local ticketData = json.decode(ticket.ticket_data)
    local prizeAmount = CalculateScratchPrize(cardType, ticketData)

    -- 更新记录（包含更新后的ticketData）
    UpdateScratchResult(ticketId, prizeAmount, ticketData)

    -- 清除中奖记录和未兑奖记录缓存，确保数据实时更新
    exports[GetCurrentResourceName()]:ClearCache("winningRecords")
    exports[GetCurrentResourceName()]:ClearCache("unclaimedRecords")

    -- 如果使用物品系统，移除物品
    if Config.Items.useScratchItems then
        local itemName = Config.Items.scratchItems[cardType]
        
        if GetResourceState('ox_inventory') == 'started' then
            -- ox_inventory方式，查找具有匹配ticketId的物品
            local items = exports.ox_inventory:Search(source, 'slots', itemName)
            if items then
                for _, item in pairs(items) do
                    if item.metadata and item.metadata.ticketId == ticketId then
                        DebugPrint("找到并移除物品: " .. itemName .. " 槽位: " .. item.slot)
                        exports.ox_inventory:RemoveItem(source, itemName, 1, nil, item.slot)
                        break
                    end
                end
            end
        else
            -- 传统框架方式
            RemovePlayerItem(source, itemName, 1)
        end
    end
    
    -- 特殊处理：5倍彩钻、中国福和乘风破浪刮刮乐给予物品奖励
    if (cardType == "scratch_caizuan" or cardType == "scratch_zhongguofu" or cardType == "scratch_chengfeng") and ticketData.rewardItems and #ticketData.rewardItems > 0 then
        -- 给予物品奖励
        local itemsGiven = {}
        local itemCounts = {}

        -- 统计物品数量
        for _, item in ipairs(ticketData.rewardItems) do
            if itemCounts[item] then
                itemCounts[item] = itemCounts[item] + 1
            else
                itemCounts[item] = 1
            end
        end

        -- 给予物品
        local allItemsGiven = true
        for item, count in pairs(itemCounts) do
            -- 解析带数量的物品格式 (例如: "money:1000")
            local itemName = item
            local itemAmount = count

            if string.find(item, ":") then
                local parts = {}
                for part in string.gmatch(item, "([^:]+)") do
                    table.insert(parts, part)
                end
                if #parts == 2 then
                    itemName = parts[1]
                    itemAmount = tonumber(parts[2]) * count -- 单个物品数量 * 获得次数
                end
            end

            local success = GivePlayerItem(source, itemName, itemAmount)
            if success then
                local displayName = GetItemDisplayName(itemName)
                if itemName == "money" then
                    table.insert(itemsGiven, displayName .. " ¥" .. itemAmount)
                else
                    table.insert(itemsGiven, displayName .. " x" .. itemAmount)
                end
                DebugPrint("自动兑奖成功给予玩家物品: " .. itemName .. " x" .. itemAmount)
            else
                allItemsGiven = false
                DebugPrint("自动兑奖给予玩家物品失败: " .. itemName .. " x" .. itemAmount)
            end
        end

        if allItemsGiven then
            -- 所有物品都给予成功
            ClaimPrize(ticketId, 'scratch')

            -- 记录交易明细
            local playerIdentifier = GetPlayerIdentifier(source)
            local playerName = GetPlayerName(source)
            local cardName = cardType == "scratch_caizuan" and "5倍彩钻" or (cardType == "scratch_zhongguofu" and "中国福" or (cardType == "scratch_chengfeng" and "乘风破浪" or "未知"))
            exports[GetCurrentResourceName()]:LogTransaction('prize', 0, cardName .. '刮刮乐自动兑奖: ' .. table.concat(itemsGiven, ", "), playerIdentifier, playerName)

            DebugPrint("^2[彩票系统] ^7" .. cardName .. "刮刮乐自动兑奖成功，给予物品: " .. table.concat(itemsGiven, ", "))

            -- 发送结果给客户端
            TriggerClientEvent('lottery:scratchResult', source, {
                action = 'scratchResult',
                result = {
                    prizeAmount = 0, -- 彩钻刮刮乐不给金钱
                    isWinning = true,
                    itemRewards = itemsGiven,
                    cardType = cardType
                }
            })

            -- 发送通知
            SendNotification(source, "恭喜您获得物品奖励：" .. table.concat(itemsGiven, ", "), "success")
        else
            -- 部分物品给予失败
            SendNotification(source, "自动兑奖失败，背包空间不足或物品系统错误", "error")
            TriggerClientEvent('lottery:scratchResult', source, {
                action = 'scratchResult',
                result = {
                    prizeAmount = 0,
                    isWinning = true,
                    paymentFailed = true,
                    message = "自动兑奖失败，背包空间不足或物品系统错误",
                    cardType = cardType
                }
            })
        end
        return
    end

    -- 判断奖金金额，大于等于10000需要到兑奖中心兑奖
    if prizeAmount >= 10000 then
        -- 大额奖金，不自动兑奖，提示去兑奖中心
        DebugPrint("^2[彩票系统] ^7检测到大额奖金: " .. prizeAmount .. "，需要去兑奖中心兑奖")
        
        -- 发送结果给客户端，但告知需要去兑奖中心
        TriggerClientEvent('lottery:scratchResult', source, {
            action = 'scratchResult',
            result = {
                prizeAmount = prizeAmount,
                isWinning = true,
                paymentFailed = true,
                message = "恭喜您中得大额奖金！请前往兑奖中心领取奖励。"
            }
        })
        
        -- 发送通知
        SendNotification(source, "恭喜您中得大额奖金：" .. prizeAmount .. "！请前往兑奖中心领取奖励。", "success")
        return
    end
    
    -- 自动兑奖，从彩票店账户中扣除
    if prizeAmount > 0 then
        if Config.Framework == "ESX" then
            -- 使用esx_addonaccount替代直接数据库查询
            TriggerEvent('esx_addonaccount:getSharedAccount', 'society_lottery', function(societyAccount)
                if societyAccount and societyAccount.money >= prizeAmount then
                    -- 账户余额足够，尝试从账户扣除实际支付金额
                    -- 使用标记变量检查操作是否成功
                    local previousMoney = societyAccount.money
                    societyAccount.removeMoney(prizeAmount)

                    -- 更新总支出统计（ESX框架需要手动更新lottery_shop_accounts表的total_payout）
                    MySQL.Async.execute('UPDATE lottery_shop_accounts SET total_payout = total_payout + ? WHERE id = 1',
                        {prizeAmount},
                        function(rowsChanged)
                            if rowsChanged > 0 then
                                DebugPrint("^2[彩票系统] ^7ESX刮刮乐兑奖总支出已更新: 增加 " .. prizeAmount)
                            else
                                DebugPrint("^1[彩票系统] ^7ESX刮刮乐兑奖总支出更新失败")
                            end
                        end
                    )

                    -- 这里不再比较余额变化，直接认为扣款成功
                    -- 验证扣款是否成功（通过检查余额变化）
                    local deductSuccess = true -- 直接假设成功

                    if deductSuccess then
                        -- 账户扣款成功，给玩家添加现金
                        AddPlayerMoney(source, prizeAmount)
                        ClaimPrize(ticketId, 'scratch')

                        -- 记录交易明细
                        local playerIdentifier = GetPlayerIdentifier(source)
                        local playerName = GetPlayerName(source)
                        exports[GetCurrentResourceName()]:LogTransaction('prize', -prizeAmount, '刮刮乐自动兑奖', playerIdentifier, playerName)

                        DebugPrint("^2[彩票系统] ^7自动兑奖成功，从彩票店账户扣除: " .. prizeAmount)
                        
                        -- 发送结果给客户端
                        TriggerClientEvent('lottery:scratchResult', source, {
                            action = 'scratchResult',
                            result = {
                                prizeAmount = prizeAmount,
                                isWinning = true
                            }
                        })
                    else
                        -- 账户扣款失败，检查是否启用职业系统
                        if Config.LotteryJob and Config.LotteryJob.enabled then
                            -- 职业系统启用，提示玩家联系彩票店工作人员
                            DebugPrint("^1[彩票系统] ^7彩票店账户扣款失败")
                            SendNotification(source, "兑奖失败，彩票店账户余额不足，请联系彩票店工作人员", "error")
                            
                            -- 发送结果给客户端，显示中奖但未领取
                            TriggerClientEvent('lottery:scratchResult', source, {
                                action = 'scratchResult',
                                result = {
                                    prizeAmount = prizeAmount,
                                    isWinning = true,
                                    paymentFailed = true,
                                    message = "兑奖失败，彩票店账户余额不足，请联系彩票店工作人员"
                                }
                            })
                        else
                            -- 职业系统未启用，直接给予奖金
                            DebugPrint("^1[彩票系统] ^7彩票店账户扣款失败，职业系统未启用，直接给予奖金")
                            AddPlayerMoney(source, prizeAmount)
                            ClaimPrize(ticketId, 'scratch')
                            
                            -- 发送结果给客户端
                            TriggerClientEvent('lottery:scratchResult', source, {
                                action = 'scratchResult',
                                result = {
                                    prizeAmount = prizeAmount,
                                    isWinning = true
                                }
                            })
                        end
                    end
                else
                    -- 账户余额不足，检查是否启用职业系统
                    if Config.LotteryJob and Config.LotteryJob.enabled then
                        -- 职业系统启用，提示玩家联系彩票店工作人员
                        DebugPrint("^1[彩票系统] ^7彩票店账户余额不足")
                        SendNotification(source, "兑奖失败，彩票店账户余额不足，请联系彩票店工作人员", "error")
                        
                        -- 发送结果给客户端，显示中奖但未领取
                        TriggerClientEvent('lottery:scratchResult', source, {
                            action = 'scratchResult',
                            result = {
                                prizeAmount = prizeAmount,
                                isWinning = true,
                                paymentFailed = true,
                                message = "兑奖失败，彩票店账户余额不足，请联系彩票店工作人员"
                            }
                        })
                    else
                        -- 职业系统未启用，直接给予奖金
                        DebugPrint("^1[彩票系统] ^7彩票店账户余额不足，职业系统未启用，直接给予奖金")
                        AddPlayerMoney(source, prizeAmount)
                        ClaimPrize(ticketId, 'scratch')
                        
                        -- 发送结果给客户端
                        TriggerClientEvent('lottery:scratchResult', source, {
                            action = 'scratchResult',
                            result = {
                                prizeAmount = prizeAmount,
                                isWinning = true
                            }
                        })
                    end
                end
            end)
        else -- Framework == "QB"
            -- 使用lottery_shop_accounts表查询余额并扣款
            MySQL.Async.fetchAll('SELECT balance FROM lottery_shop_accounts WHERE id = 1', {}, function(result)
                if result and #result > 0 and result[1].balance >= prizeAmount then
                    -- 账户余额足够，从账户扣除
                    MySQL.Async.execute('UPDATE lottery_shop_accounts SET balance = balance - ?, total_payout = total_payout + ? WHERE id = 1 AND balance >= ?', 
                        {prizeAmount, prizeAmount, prizeAmount}, 
                        function(rowsChanged)
                            if rowsChanged > 0 then
                                -- 账户扣款成功，给玩家添加现金
                                AddPlayerMoney(source, prizeAmount)
                                ClaimPrize(ticketId, 'scratch')
                                
                                -- 记录交易明细
                                local playerIdentifier = GetPlayerIdentifier(source)
                                local playerName = GetPlayerName(source)
                                exports[GetCurrentResourceName()]:LogTransaction('prize', -prizeAmount, '刮刮乐自动兑奖', playerIdentifier, playerName)
                                
                                DebugPrint("^2[彩票系统] ^7自动兑奖成功，从彩票店账户扣除: " .. prizeAmount)
                                
                                -- 发送结果给客户端
                                TriggerClientEvent('lottery:scratchResult', source, {
                                    action = 'scratchResult',
                                    result = {
                                        prizeAmount = prizeAmount,
                                        isWinning = true
                                    }
                                })
                            else
                                -- 账户扣款失败，检查是否启用职业系统
                                if Config.LotteryJob and Config.LotteryJob.enabled then
                                    -- 职业系统启用，提示玩家联系彩票店工作人员
                                    DebugPrint("^1[彩票系统] ^7彩票店账户扣款失败")
                                    SendNotification(source, "兑奖失败，彩票店账户余额不足，请联系彩票店工作人员", "error")
                                    
                                    -- 发送结果给客户端，显示中奖但未领取
                                    TriggerClientEvent('lottery:scratchResult', source, {
                                        action = 'scratchResult',
                                        result = {
                                            prizeAmount = prizeAmount,
                                            isWinning = true,
                                            paymentFailed = true,
                                            message = "兑奖失败，彩票店账户余额不足，请联系彩票店工作人员"
                                        }
                                    })
                                else
                                    -- 职业系统未启用，直接给予奖金
                                    DebugPrint("^1[彩票系统] ^7彩票店账户扣款失败，职业系统未启用，直接给予奖金")
                                    AddPlayerMoney(source, prizeAmount)
                                    ClaimPrize(ticketId, 'scratch')
                                    
                                    -- 发送结果给客户端
                                    TriggerClientEvent('lottery:scratchResult', source, {
                                        action = 'scratchResult',
                                        result = {
                                            prizeAmount = prizeAmount,
                                            isWinning = true
                                        }
                                    })
                                end
                            end
                        end
                    )
                else
                    -- 账户余额不足，检查是否启用职业系统
                    if Config.LotteryJob and Config.LotteryJob.enabled then
                        -- 职业系统启用，提示玩家联系彩票店工作人员
                        DebugPrint("^1[彩票系统] ^7彩票店账户余额不足")
                        SendNotification(source, "兑奖失败，彩票店账户余额不足，请联系彩票店工作人员", "error")
                        
                        -- 发送结果给客户端，显示中奖但未领取
                        TriggerClientEvent('lottery:scratchResult', source, {
                            action = 'scratchResult',
                            result = {
                                prizeAmount = prizeAmount,
                                isWinning = true,
                                paymentFailed = true,
                                message = "兑奖失败，彩票店账户余额不足，请联系彩票店工作人员"
                            }
                        })
                    else
                        -- 职业系统未启用，直接给予奖金
                        DebugPrint("^1[彩票系统] ^7彩票店账户余额不足，职业系统未启用，直接给予奖金")
                        AddPlayerMoney(source, prizeAmount)
                        ClaimPrize(ticketId, 'scratch')
                        
                        -- 发送结果给客户端
                        TriggerClientEvent('lottery:scratchResult', source, {
                            action = 'scratchResult',
                            result = {
                                prizeAmount = prizeAmount,
                                isWinning = true
                            }
                        })
                    end
                end
            end)
        end
    else
        -- 没有中奖，直接发送结果
        TriggerClientEvent('lottery:scratchResult', source, {
            action = 'scratchResult',
            result = {
                prizeAmount = prizeAmount,
                isWinning = false
            }
        })
    end
end)

-- 刮奖区域
RegisterNetEvent('lottery:scratchArea')
AddEventHandler('lottery:scratchArea', function(data)
    local source = source
    -- 这里可以记录刮奖过程，目前只需要记录
end)

-- 获取刮刮乐统计
RegisterNetEvent('lottery:getScratchStats')
AddEventHandler('lottery:getScratchStats', function()
    local source = source
    local playerIdentifier = GetPlayerIdentifier(source)
    
    if not playerIdentifier then
        DebugPrint("^1[彩票系统] ^7获取刮刮乐统计错误: 无法获取玩家标识符")
        TriggerClientEvent('lottery:receiveScratchStats', source, {})
        return
    end
    
    DebugPrint("^3[彩票系统] ^7获取刮刮乐统计 - 玩家ID: " .. playerIdentifier)
    
    -- 使用异步版本的 GetPlayerLotteryStats
    GetPlayerLotteryStats(playerIdentifier, 'scratch', function(stats)
        -- 确保返回的是数组，即使是空的
        stats = stats or {}

        if #stats == 0 then
            DebugPrint("^3[彩票系统] ^7玩家 " .. playerIdentifier .. " 没有刮刮乐记录，返回空数组")
        else
            DebugPrint("^2[彩票系统] ^7玩家 " .. playerIdentifier .. " 刮刮乐记录数量: " .. #stats)
            for i, stat in ipairs(stats) do
                DebugPrint("^2[彩票系统] ^7刮刮乐类型: " .. stat.card_type .. ", 数量: " .. stat.count .. ", 总奖金: " .. (stat.total_prize or 0))
            end
        end

        TriggerClientEvent('lottery:receiveScratchStats', source, stats)
    end)
end)

-- 生成刮刮乐票据数据
function GenerateScratchTicketData(cardType)
    local cardConfig = Config.ScratchCards[cardType]
    local ticketData = {}
    
    if cardType == "scratch_xixiangfeng" then
        -- 喜相逢：10个区域，每个区域有符号和金额
        ticketData.areas = {}
        local symbols = {"♠", "♥", "♦", "♣", "★", "☆", "◆", "●", "■", "▲"} -- 普通符号列表，不包含喜和囍
        
        -- 从配置文件获取金额配置
        local regularAmounts = cardConfig.regularAmounts or {5, 10, 20, 50, 100, 200, 500, 1000, 5000, 10000,50000,300000}
        
        -- 为喜囍符号准备加权金额
        local specialAmounts = {}
        local amountWeights = {}
        local totalWeight = 0
        
        -- 处理金额概率配置（仅用于喜和囍符号）
        if cardConfig.amountRates then
            -- 使用配置的金额概率
            for amount, weight in pairs(cardConfig.amountRates) do
                table.insert(specialAmounts, amount)
                table.insert(amountWeights, weight)
                totalWeight = totalWeight + weight
            end
        else
            -- 使用配置中定义的特殊符号金额列表（均等概率）
            specialAmounts = cardConfig.specialAmounts or {5, 10, 20, 50, 100, 200, 500, 1000, 5000, 10000}
        end
        
        -- 检查是否设置了全部都是喜囍的模式
        local allSpecialRate = Config.ScratchSymbols and Config.ScratchSymbols.allSpecialRate or 2 -- 默认2%
        local useAllSpecial = (math.random(1, 100) <= allSpecialRate)
        
        -- 从配置获取喜和囍的出现概率
        local xiRate = Config.ScratchSymbols and Config.ScratchSymbols.xiRate or 10 -- 默认10%
        local xiXiRate = Config.ScratchSymbols and Config.ScratchSymbols.xiXiRate or 5 -- 默认5%
        
        for i = 1, 10 do
            -- 根据概率决定符号
            local symbol
            if useAllSpecial then
                -- 在全特殊模式下，只使用喜和囍
                if math.random(1, 100) <= 70 then  -- 70%概率出现"喜"
                    symbol = "喜"
                else  -- 30%概率出现"囍"
                    symbol = "囍"
                end
            else
                -- 正常模式，使用准确的概率范围
                local rand = math.random(1, 100)
                if rand <= xiRate then  -- xiRate概率出现"喜"
                    symbol = "喜"
                elseif rand <= (xiRate + xiXiRate) and rand > xiRate then  -- xiXiRate概率出现"囍"，注意检查范围
                    symbol = "囍"
                else
                    symbol = symbols[math.random(#symbols)]  -- 随机选择普通符号
                end
            end
            
            -- 选择金额
            local amount
            
            -- 对喜和囍符号使用加权概率，对普通符号使用随机金额
            if symbol == "喜" or symbol == "囍" then
                -- 特殊符号使用加权随机
                if cardConfig.amountRates then
                    -- 使用加权随机
                    local r = math.random() * totalWeight
                    local cumulativeWeight = 0
                    
                    for i = 1, #specialAmounts do
                        cumulativeWeight = cumulativeWeight + amountWeights[i]
                        if r <= cumulativeWeight then
                            amount = specialAmounts[i]
                            break
                        end
                    end
                    
                    -- 防止没有选中，使用最后一个金额
                    if not amount then
                        amount = specialAmounts[#specialAmounts]
                    end
                else
                    -- 使用均等概率
                    amount = specialAmounts[math.random(#specialAmounts)]
                end
            else
                -- 普通符号使用随机金额（可能出现大奖数字，增加刺激感）
                amount = regularAmounts[math.random(#regularAmounts)]
            end
            
            table.insert(ticketData.areas, {
                symbol = symbol,
                amount = amount
            })
        end
        
    elseif cardType == "scratch_fusong" then
        -- 福鼠送彩：中奖号码 + 5x5数字网格
        -- 确保winningNumber是数字类型
        ticketData.winningNumber = tonumber(math.random(1, 99))
        ticketData.myNumbers = {}
        
        -- 记录福鼠号码到日志
        DebugPrint("生成新福鼠号码: " .. ticketData.winningNumber)
        
        -- 使用新的配置方式: fusongMatchRates
        if Config.ScratchSymbols and Config.ScratchSymbols.fusongMatchRates then
            -- 计算总权重
            local totalWeight = 0
            local possibleCounts = {}
            
            for count, weight in pairs(Config.ScratchSymbols.fusongMatchRates) do
                table.insert(possibleCounts, count)
                totalWeight = totalWeight + weight
            end
            
            -- 对可能的匹配数量进行排序，确保顺序一致
            table.sort(possibleCounts, function(a, b) return tonumber(a) < tonumber(b) end)
            
            -- 添加调试信息，显示所有可能的匹配数和权重
            DebugPrint("福鼠送彩可能的匹配数量和权重:")
            for _, count in ipairs(possibleCounts) do
                DebugPrint("匹配数: " .. count .. " 权重: " .. Config.ScratchSymbols.fusongMatchRates[count])
            end
            
            -- 随机决定放置多少个福鼠号码
            local randomWeight = math.random() * totalWeight
            DebugPrint("福鼠送彩随机权重: " .. randomWeight .. " / 总权重: " .. totalWeight)
            local cumulativeWeight = 0
            local foundMatch = false  -- 添加标志，检查是否找到了匹配
            
            for _, count in ipairs(possibleCounts) do
                local oldCumulativeWeight = cumulativeWeight
                cumulativeWeight = cumulativeWeight + Config.ScratchSymbols.fusongMatchRates[count]
                DebugPrint("检查匹配数 " .. count .. ": 累积权重范围 " .. oldCumulativeWeight .. " - " .. cumulativeWeight)
                if randomWeight <= cumulativeWeight then
                    matchCount = tonumber(count)  -- 确保转换为数字类型
                    DebugPrint("选择匹配数: " .. count .. " (随机值: " .. randomWeight .. " 在范围内)")
                    foundMatch = true
                    break
                end
            end

            -- 如果没有找到匹配（可能是由于浮点误差），使用最后一个值
            if not foundMatch then
                DebugPrint("警告：没有找到匹配的概率区间，使用默认值")
                if #possibleCounts > 0 then
                    matchCount = tonumber(possibleCounts[1])  -- 确保转换为数字类型
                    DebugPrint("使用默认匹配数: " .. matchCount)
                else
                    matchCount = 0  -- 保底值
                    DebugPrint("没有可用的匹配数，使用0")
                end
            end
            
            DebugPrint("福鼠送彩将随机放置 " .. matchCount .. " 个中奖号码")
        else
            -- 兼容旧配置
            local winNumRate = Config.ScratchSymbols and Config.ScratchSymbols.fusongWinNumRate or 30
            local allMatchRate = Config.ScratchSymbols and Config.ScratchSymbols.fusongAllMatchRate or 1
            local maxMatches = Config.ScratchSymbols and Config.ScratchSymbols.fusongMaxMatches or 5
            
            -- 检查是否为全匹配模式
            if math.random(1, 100) <= allMatchRate then
                DebugPrint("福鼠送彩触发全匹配模式！")
                matchCount = 25  -- 全都是中奖号码
            else
                -- 根据概率决定数量
                matchCount = math.ceil(maxMatches * winNumRate / 100)
                matchCount = math.min(matchCount, 25)
                matchCount = math.max(matchCount, 1)
            end
            
            DebugPrint("福鼠送彩使用旧配置，将放置 " .. matchCount .. " 个中奖号码")
        end
        
        -- 先填充所有25个位置为非中奖号码
        for i = 1, 25 do
            local randomNumber
            local winNumber = tonumber(ticketData.winningNumber)
            repeat
                randomNumber = math.random(1, 99)
            until randomNumber ~= winNumber
            
            table.insert(ticketData.myNumbers, randomNumber)
        end
        
        -- 创建一个包含25个位置的数组，用于随机选择
        local positions = {}
        for i = 1, 25 do
            positions[i] = i
        end
        
        -- 随机打乱位置数组 (Fisher-Yates 洗牌算法)
        for i = #positions, 2, -1 do
            local j = math.random(i)
            positions[i], positions[j] = positions[j], positions[i]
        end
        
        -- 放置福鼠号码，替换部分非中奖号码
        local placedCount = 0
        -- 如果matchCount为0，则跳过放置福鼠号码的步骤
        if matchCount > 0 then
            for i = 1, #positions do
                if placedCount >= matchCount then
                    break  -- 已经放置了足够多的中奖号码
                end
                
                local pos = positions[i]
                -- 替换为中奖号码，确保类型一致
                ticketData.myNumbers[pos] = tonumber(ticketData.winningNumber)
                placedCount = placedCount + 1
                
                local row = math.ceil(pos / 5)
                local col = ((pos-1) % 5 + 1)
                DebugPrint("在第" .. row .. "行第" .. col .. "列放置福鼠号码")
            end
        else
            DebugPrint("福鼠送彩本次不放置福鼠号码")
        end
        
        -- 统计每行是否有福鼠号码
        local rowHasFuSong = {false, false, false, false, false}
        for i = 1, #ticketData.myNumbers do
            -- 确保类型一致，转换为数字进行比较
            local myNumber = tonumber(ticketData.myNumbers[i])
            local winNumber = tonumber(ticketData.winningNumber)
            if myNumber == winNumber then
                local row = math.ceil(i / 5)
                rowHasFuSong[row] = true
            end
        end
        
        -- 输出福鼠号码分布情况
        for row = 1, 5 do
            DebugPrint("第" .. row .. "行" .. (rowHasFuSong[row] and "有" or "没有") .. "福鼠号码")
        end
        
        -- 生成行金额数组，根据是否有福鼠号码决定生成方式
        ticketData.rowAmounts = {}
        
        -- 检查是否有行金额权重配置
        if cardConfig.rowAmountRates then
            -- 使用权重随机选择每行金额
            local amountValues = {}
            local amountWeights = {}
            local totalWeight = 0
            
            -- 收集配置的金额和权重
            for amount, weight in pairs(cardConfig.rowAmountRates) do
                table.insert(amountValues, amount)
                table.insert(amountWeights, weight)
                totalWeight = totalWeight + weight
            end
            
            -- 为每行选择金额
            for row = 1, 5 do
                local selectedAmount = 0
                
                if rowHasFuSong[row] then
                    -- 有福鼠号码的行：使用配置的权重分布
                    local r = math.random() * totalWeight
                    local cumulativeWeight = 0
                    
                    for i = 1, #amountValues do
                        cumulativeWeight = cumulativeWeight + amountWeights[i]
                        if r <= cumulativeWeight then
                            selectedAmount = amountValues[i]
                            break
                        end
                    end
                    
                    -- 默认使用第一个金额（防止意外情况）
                    if selectedAmount == 0 then
                        selectedAmount = amountValues[1]
                    end
                    
                    DebugPrint("第" .. row .. "行有福鼠号码，使用权重生成金额: " .. selectedAmount)
                else
                    -- 没有福鼠号码的行：使用配置中的概率生成金额
                    local noMatchConfig = Config.ScratchSymbols.fusongNoMatchAmountRates or {highAmount = 5, mediumAmount = 20, lowAmount = 75}
                    local amountRanges = Config.ScratchSymbols.fusongNoMatchAmounts or {
                        low = {10, 20, 50, 100, 200},
                        medium = {500, 1000, 2000, 5000},
                        high = {10000, 50000, 100000, 250000}
                    }
                    
                    -- 确保配置存在有效值
                    local highProb = noMatchConfig.highAmount or 5
                    local mediumProb = noMatchConfig.mediumAmount or 20
                    local lowProb = 100 - highProb - mediumProb
                    
                    -- 随机决定金额档位
                    local rand = math.random(100)
                    if rand <= highProb then
                        -- 超大奖金额
                        local highAmounts = amountRanges.high or {10000, 50000, 100000, 250000}
                        selectedAmount = highAmounts[math.random(#highAmounts)]
                        DebugPrint("第" .. row .. "行没有福鼠号码，生成超大奖金: " .. selectedAmount)
                    elseif rand <= (highProb + mediumProb) then
                        -- 中等奖金额
                        local mediumAmounts = amountRanges.medium or {500, 1000, 2000, 5000}
                        selectedAmount = mediumAmounts[math.random(#mediumAmounts)]
                        DebugPrint("第" .. row .. "行没有福鼠号码，生成中等奖金: " .. selectedAmount)
                    else
                        -- 小额奖金额
                        local lowAmounts = amountRanges.low or {10, 20, 50, 100, 200}
                        selectedAmount = lowAmounts[math.random(#lowAmounts)]
                        DebugPrint("第" .. row .. "行没有福鼠号码，生成小额奖金: " .. selectedAmount)
                    end
                end
                
                table.insert(ticketData.rowAmounts, selectedAmount)
            end
        else
            -- 使用旧版固定行金额
            ticketData.rowAmounts = cardConfig.rowPrizes or {10, 20, 50, 100, 500}
            DebugPrint("福鼠送彩使用固定行金额")
        end
        
        -- 输出生成的号码矩阵供调试
        DebugPrint("福鼠送彩生成的号码矩阵:")
        for row = 1, 5 do
            local rowNumbers = {}
            for col = 1, 5 do
                local index = (row - 1) * 5 + col
                table.insert(rowNumbers, ticketData.myNumbers[index])
            end
            DebugPrint("第" .. row .. "行: " .. table.concat(rowNumbers, ", ") .. " 金额: " .. ticketData.rowAmounts[row])
        end
        
    elseif cardType == "scratch_yaocai" then
        -- 耀出彩：中奖号码 + 我的号码
        ticketData.winningNumbers = {}
        ticketData.myNumbers = {}
        ticketData.numberAmounts = {} -- 存储每个号码对应的金额
        
        -- 生成2个中奖号码
        for i = 1, 2 do
            table.insert(ticketData.winningNumbers, math.random(1, 50))
        end
        
        -- 从配置中获取耀出彩号码的概率参数
        local winNumRate = Config.ScratchSymbols and Config.ScratchSymbols.yaocaiWinNumRate or 20 -- 默认20%
        local allMatchRate = Config.ScratchSymbols and Config.ScratchSymbols.yaocaiAllMatchRate or 1 -- 默认1%
        local maxMatches = Config.ScratchSymbols and Config.ScratchSymbols.yaocaiMaxMatches or 5 -- 默认最多5个
        
        -- 检查是否为全匹配模式
        local useAllWinningNumbers = (math.random(1, 100) <= allMatchRate)
        
        -- 如果是全匹配模式，则设置更多的中奖号码
        local matchTarget = 0
        if useAllWinningNumbers then
            DebugPrint("耀出彩触发全匹配模式！")
            matchTarget = math.min(15, 25) -- 最多15个中奖号码
        else
            -- 使用新的配置方式: yaocaiMatchRates
            if Config.ScratchSymbols and Config.ScratchSymbols.yaocaiMatchRates then
                -- 计算总权重
                local totalWeight = 0
                local possibleCounts = {}
                
                for count, weight in pairs(Config.ScratchSymbols.yaocaiMatchRates) do
                    table.insert(possibleCounts, count)
                    totalWeight = totalWeight + weight
                end
                
                -- 对可能的匹配数量进行排序，确保顺序一致
                table.sort(possibleCounts, function(a, b) return tonumber(a) < tonumber(b) end)
                
                -- 添加调试信息，显示所有可能的匹配数和权重
                DebugPrint("耀出彩可能的匹配数量和权重:")
                for _, count in ipairs(possibleCounts) do
                    DebugPrint("匹配数: " .. count .. " 权重: " .. Config.ScratchSymbols.yaocaiMatchRates[count])
                end
                
                -- 随机决定放置多少个中奖号码
                local randomWeight = math.random() * totalWeight
                DebugPrint("耀出彩随机权重: " .. randomWeight .. " / 总权重: " .. totalWeight)
                local cumulativeWeight = 0
                local foundMatch = false  -- 添加标志，检查是否找到了匹配
                
                for _, count in ipairs(possibleCounts) do
                    local oldCumulativeWeight = cumulativeWeight
                    cumulativeWeight = cumulativeWeight + Config.ScratchSymbols.yaocaiMatchRates[count]
                    DebugPrint("检查匹配数 " .. count .. ": 累积权重范围 " .. oldCumulativeWeight .. " - " .. cumulativeWeight)
                    if randomWeight <= cumulativeWeight then
                        matchTarget = tonumber(count)  -- 确保转换为数字类型
                        DebugPrint("选择匹配数: " .. count .. " (随机值: " .. randomWeight .. " 在范围内)")
                        foundMatch = true
                        break
                    end
                end

                -- 如果没有找到匹配（可能是由于浮点误差），使用第一个值
                if not foundMatch then
                    DebugPrint("警告：没有找到匹配的概率区间，使用默认值")
                    if #possibleCounts > 0 then
                        matchTarget = tonumber(possibleCounts[1])  -- 确保转换为数字类型
                        DebugPrint("使用默认匹配数: " .. matchTarget)
                    else
                        matchTarget = 0  -- 保底值
                        DebugPrint("没有可用的匹配数，使用0")
                    end
                end
            else
                -- 兼容旧配置
                -- 随机决定最多有几个匹配
                matchTarget = math.random(0, maxMatches)
                DebugPrint("耀出彩将随机放置 " .. matchTarget .. " 个中奖号码 (直接概率模式)")
            end
        end
        
        local matchCount = 0
        
        -- 创建一个包含25个位置的数组，用于随机选择
        local positions = {}
        for i = 1, 25 do
            positions[i] = i
        end
        
        -- 随机打乱位置数组 (Fisher-Yates 洗牌算法)
        for i = #positions, 2, -1 do
            local j = math.random(i)
            positions[i], positions[j] = positions[j], positions[i]
        end
        
        -- 先填充所有25个位置为非中奖号码
        for i = 1, 25 do
            local randomNumber
            repeat
                randomNumber = math.random(1, 50)
                -- 确保不是中奖号码
                local isWinning = false
                for _, win in ipairs(ticketData.winningNumbers) do
                    if randomNumber == win then
                        isWinning = true
                        break
                    end
                end
            until not isWinning
            
            table.insert(ticketData.myNumbers, randomNumber)
            table.insert(ticketData.numberAmounts, 0) -- 初始化金额为0
        end
        
        -- 放置中奖号码，替换部分非中奖号码
        if matchTarget > 0 then -- 确保有中奖号码需要放置
            for i = 1, #positions do
                if matchCount >= matchTarget then
                    break  -- 已经放置了足够多的中奖号码
                end
                
                local pos = positions[i]
                -- 替换为中奖号码
                ticketData.myNumbers[pos] = ticketData.winningNumbers[math.random(#ticketData.winningNumbers)]
                matchCount = matchCount + 1
                
                DebugPrint("在位置" .. pos .. "放置耀出彩中奖号码: " .. ticketData.myNumbers[pos])
            end
        else
            DebugPrint("耀出彩本次不放置中奖号码")
        end
        
        -- 获取配置
        local amountRates = Config.ScratchSymbols.yaocaiAmountRates or {
            winningHighRate = 20,
            nonWinningHighRate = 5,
            nonWinningMediumRate = 20
        }
        
        local amountRanges = Config.ScratchSymbols.yaocaiAmounts or {
            low = {10, 20, 50, 100, 200},
            medium = {500, 1000, 2000},
            high = {5000, 10000, 20000, 50000}
        }
        
        -- 为每个号码生成金额
        for i = 1, #ticketData.myNumbers do
            local myNumber = ticketData.myNumbers[i]
            local isWinningNumber = false
            
            -- 检查是否是中奖号码
            for _, win in ipairs(ticketData.winningNumbers) do
                if myNumber == win then
                    isWinningNumber = true
                    break
                end
            end
            
            -- 生成金额
            local amount = 0
            
            if isWinningNumber then
                -- 中奖号码使用卡片配置中的金额权重
                local cardConfig = Config.ScratchCards["scratch_yaocai"]
                if cardConfig and cardConfig.winningAmountRates then
                    -- 使用配置的金额权重
                    local amountValues = {}
                    local amountWeights = {}
                    local totalWeight = 0
                    
                    -- 收集所有金额和对应权重
                    for amt, weight in pairs(cardConfig.winningAmountRates) do
                        table.insert(amountValues, amt)
                        table.insert(amountWeights, weight)
                        totalWeight = totalWeight + weight
                    end
                    
                    -- 按权重随机选择金额
                    local r = math.random() * totalWeight
                    local cumulativeWeight = 0
                    
                    for j = 1, #amountValues do
                        cumulativeWeight = cumulativeWeight + amountWeights[j]
                        if r <= cumulativeWeight then
                            amount = amountValues[j]
                            break
                        end
                    end
                    
                    -- 防止没有选中，使用最小金额
                    if amount == 0 then
                        amount = amountValues[1]
                    end
                    
                    DebugPrint("位置" .. i .. "中奖号码" .. myNumber .. "使用配置权重生成金额:" .. amount)
                else
                    -- 如果没有配置，使用高、中、低概率档位
                    local rand = math.random(100)
                    if rand <= amountRates.winningHighRate then
                        -- 高额金额
                        amount = amountRanges.high[math.random(#amountRanges.high)]
                        DebugPrint("位置" .. i .. "中奖号码" .. myNumber .. "随机生成高额金额:" .. amount)
                    else
                        -- 中等金额
                        amount = amountRanges.medium[math.random(#amountRanges.medium)]
                        DebugPrint("位置" .. i .. "中奖号码" .. myNumber .. "随机生成中等金额:" .. amount)
                    end
                end
            else
                -- 非中奖号码使用低中高三个档位
                local rand = math.random(100)
                if rand <= amountRates.nonWinningHighRate then
                    -- 高额金额
                    amount = amountRanges.high[math.random(#amountRanges.high)]
                    DebugPrint("位置" .. i .. "非中奖号码" .. myNumber .. "随机生成高额金额:" .. amount)
                elseif rand <= (amountRates.nonWinningHighRate + amountRates.nonWinningMediumRate) then
                    -- 中等金额
                    amount = amountRanges.medium[math.random(#amountRanges.medium)]
                    DebugPrint("位置" .. i .. "非中奖号码" .. myNumber .. "随机生成中等金额:" .. amount)
                else
                    -- 低额金额
                    amount = amountRanges.low[math.random(#amountRanges.low)]
                    DebugPrint("位置" .. i .. "非中奖号码" .. myNumber .. "随机生成低额金额:" .. amount)
                end
            end
            
            -- 存储金额
            ticketData.numberAmounts[i] = amount
            DebugPrint("位置" .. i .. "号码" .. myNumber .. "金额:" .. amount)
        end
        
        DebugPrint("耀出彩生成完成，中奖号码: " .. table.concat(ticketData.winningNumbers, ","))
    elseif cardType == "scratch_caizuan" then
        -- 5倍彩钻：中奖号码 + 我的号码 + 钻石图案
        ticketData.winningNumbers = {}
        ticketData.myNumbers = {}
        ticketData.numberItems = {} -- 存储每个号码对应的物品
        ticketData.diamondPositions = {} -- 存储钻石图案位置

        -- 先生成2个我的号码
        for i = 1, 2 do
            table.insert(ticketData.myNumbers, math.random(1, 50))
            table.insert(ticketData.numberItems, "") -- 初始化物品为空
        end

        -- 生成20个中奖号码（先填充为非我的号码的随机数）
        for i = 1, 20 do
            local randomNumber
            repeat
                randomNumber = math.random(1, 50)
                -- 确保不是我的号码
                local isMyNumber = false
                for _, myNum in ipairs(ticketData.myNumbers) do
                    if randomNumber == myNum then
                        isMyNumber = true
                        break
                    end
                end
            until not isMyNumber

            table.insert(ticketData.winningNumbers, randomNumber)
        end

        -- 从配置中获取彩钻号码的概率参数
        local matchTarget = 0

        -- 使用新的配置方式: caizuanMatchRates
        if Config.ScratchSymbols and Config.ScratchSymbols.caizuanMatchRates then
            -- 计算总权重
            local totalWeight = 0
            local possibleCounts = {}

            for count, weight in pairs(Config.ScratchSymbols.caizuanMatchRates) do
                table.insert(possibleCounts, count)
                totalWeight = totalWeight + weight
            end

            -- 对可能的匹配数量进行排序，确保顺序一致
            table.sort(possibleCounts, function(a, b) return tonumber(a) < tonumber(b) end)

            -- 添加调试信息，显示所有可能的匹配数和权重
            DebugPrint("5倍彩钻可能的匹配数量和权重:")
            for _, count in ipairs(possibleCounts) do
                DebugPrint("匹配数: " .. count .. " 权重: " .. Config.ScratchSymbols.caizuanMatchRates[count])
            end

            -- 随机决定放置多少个中奖号码
            local randomWeight = math.random() * totalWeight
            DebugPrint("5倍彩钻随机权重: " .. randomWeight .. " / 总权重: " .. totalWeight)
            local cumulativeWeight = 0
            local foundMatch = false

            for _, count in ipairs(possibleCounts) do
                local oldCumulativeWeight = cumulativeWeight
                cumulativeWeight = cumulativeWeight + Config.ScratchSymbols.caizuanMatchRates[count]
                DebugPrint("检查匹配数 " .. count .. ": 累积权重范围 " .. oldCumulativeWeight .. " - " .. cumulativeWeight)
                if randomWeight <= cumulativeWeight then
                    matchTarget = tonumber(count)
                    DebugPrint("选择匹配数: " .. count .. " (随机值: " .. randomWeight .. " 在范围内)")
                    foundMatch = true
                    break
                end
            end

            -- 如果没有找到匹配，使用默认值
            if not foundMatch then
                DebugPrint("警告：没有找到匹配的概率区间，使用默认值")
                if #possibleCounts > 0 then
                    matchTarget = tonumber(possibleCounts[1])
                    DebugPrint("使用默认匹配数: " .. matchTarget)
                else
                    matchTarget = 0
                    DebugPrint("没有可用的匹配数，使用0")
                end
            end
        else
            -- 兼容旧配置，默认随机0-5个匹配
            matchTarget = math.random(0, 5)
            DebugPrint("5倍彩钻将随机放置 " .. matchTarget .. " 个中奖号码 (直接概率模式)")
        end

        local matchCount = 0

        -- 创建一个包含20个中奖号码位置的数组，用于随机选择
        local positions = {}
        for i = 1, 20 do
            positions[i] = i
        end

        -- 随机打乱位置数组
        for i = #positions, 2, -1 do
            local j = math.random(i)
            positions[i], positions[j] = positions[j], positions[i]
        end

        -- 根据matchTarget将部分中奖号码替换为我的号码
        if matchTarget > 0 then
            for i = 1, math.min(matchTarget, #positions) do
                if matchCount >= matchTarget then
                    break
                end

                local pos = positions[i]
                -- 替换为我的号码之一
                ticketData.winningNumbers[pos] = ticketData.myNumbers[math.random(#ticketData.myNumbers)]
                matchCount = matchCount + 1

                DebugPrint("在中奖号码位置" .. pos .. "放置我的号码: " .. ticketData.winningNumbers[pos])
            end
        else
            DebugPrint("5倍彩钻本次不放置匹配的中奖号码")
        end

        -- 生成钻石图案
        local diamondTarget = 0

        -- 使用配置的钻石图案概率
        if Config.ScratchSymbols and Config.ScratchSymbols.caizuanDiamondRates then
            local totalWeight = 0
            local possibleCounts = {}

            for count, weight in pairs(Config.ScratchSymbols.caizuanDiamondRates) do
                table.insert(possibleCounts, count)
                totalWeight = totalWeight + weight
            end

            table.sort(possibleCounts, function(a, b) return tonumber(a) < tonumber(b) end)

            local randomWeight = math.random() * totalWeight
            local cumulativeWeight = 0

            for _, count in ipairs(possibleCounts) do
                cumulativeWeight = cumulativeWeight + Config.ScratchSymbols.caizuanDiamondRates[count]
                if randomWeight <= cumulativeWeight then
                    diamondTarget = tonumber(count)
                    break
                end
            end

            DebugPrint("5倍彩钻将生成 " .. diamondTarget .. " 个钻石图案")
        end

        -- 在20个中奖号码中随机选择位置放置钻石图案
        if diamondTarget > 0 then
            local diamondPositions = {}
            for i = 1, 20 do
                diamondPositions[i] = i
            end

            -- 随机打乱位置
            for i = #diamondPositions, 2, -1 do
                local j = math.random(i)
                diamondPositions[i], diamondPositions[j] = diamondPositions[j], diamondPositions[i]
            end

            -- 选择前diamondTarget个位置作为钻石图案位置
            for i = 1, math.min(diamondTarget, #diamondPositions) do
                table.insert(ticketData.diamondPositions, diamondPositions[i])
                DebugPrint("在中奖号码位置" .. diamondPositions[i] .. "放置钻石图案")
            end
        end

        -- 获取物品配置
        local cardConfig = Config.ScratchCards["scratch_caizuan"]

        -- 我的号码不需要生成物品，因为奖励来自匹配的中奖号码

        -- 为中奖号码生成物品（用于显示）
        ticketData.winningNumberItems = {}
        ticketData.winningNumberItemsDisplay = {} -- 用于显示的中文名称

        -- 获取所有可能的物品列表（用于非匹配时随机显示）
        local allItems = {}
        if cardConfig and cardConfig.winningItemRates then
            for itm, _ in pairs(cardConfig.winningItemRates) do
                table.insert(allItems, itm)
            end
        else
            -- 默认物品列表
            allItems = {"bread", "water", "bandage", "phone", "lockpick", "diamond", "gold", "weapon_pistol", "money", "car_key"}
        end

        for i = 1, #ticketData.winningNumbers do
            local item = ""
            local winNum = ticketData.winningNumbers[i]

            -- 检查这个中奖号码是否会与我的号码匹配，或者是钻石图案位置
            local willMatch = false

            -- 检查是否是钻石图案位置
            local isDiamondPosition = false
            for _, pos in ipairs(ticketData.diamondPositions) do
                if pos == i then
                    isDiamondPosition = true
                    willMatch = true -- 钻石图案也算匹配
                    break
                end
            end

            -- 如果不是钻石图案，检查号码是否匹配
            if not isDiamondPosition then
                for _, myNum in ipairs(ticketData.myNumbers) do
                    if tonumber(winNum) == tonumber(myNum) then
                        willMatch = true
                        break
                    end
                end
            end

            if willMatch then
                -- 匹配时：使用配置的物品权重
                if cardConfig and cardConfig.winningItemRates then
                    local itemValues = {}
                    local itemWeights = {}
                    local totalWeight = 0

                    -- 收集所有物品和对应权重
                    for itm, weight in pairs(cardConfig.winningItemRates) do
                        table.insert(itemValues, itm)
                        table.insert(itemWeights, weight)
                        totalWeight = totalWeight + weight
                    end

                    -- 按权重随机选择物品
                    local r = math.random() * totalWeight
                    local cumulativeWeight = 0

                    for j = 1, #itemValues do
                        cumulativeWeight = cumulativeWeight + itemWeights[j]
                        if r <= cumulativeWeight then
                            item = itemValues[j]
                            break
                        end
                    end

                    -- 防止没有选中，使用第一个物品
                    if item == "" then
                        item = itemValues[1] or "bread"
                    end

                    if isDiamondPosition then
                        DebugPrint("中奖号码位置" .. i .. " 钻石图案匹配，使用权重生成物品:" .. item)
                    else
                        DebugPrint("中奖号码位置" .. i .. " 号码" .. winNum .. "匹配，使用权重生成物品:" .. item)
                    end
                else
                    item = "bread"
                    if isDiamondPosition then
                        DebugPrint("中奖号码位置" .. i .. " 钻石图案匹配，使用默认物品:" .. item)
                    else
                        DebugPrint("中奖号码位置" .. i .. " 号码" .. winNum .. "匹配，使用默认物品:" .. item)
                    end
                end
            else
                -- 不匹配时：随机显示物品
                item = allItems[math.random(#allItems)]
                DebugPrint("中奖号码位置" .. i .. " 号码" .. winNum .. "不匹配，随机显示物品:" .. item)
            end

            table.insert(ticketData.winningNumberItems, item)

            -- 生成显示名称，如果是带数量的money则显示金额
            local displayName = GetItemDisplayName(item)
            if string.find(item, "money:") then
                local parts = {}
                for part in string.gmatch(item, "([^:]+)") do
                    table.insert(parts, part)
                end
                if #parts == 2 then
                    displayName = "现金 ¥" .. parts[2]
                end
            end
            table.insert(ticketData.winningNumberItemsDisplay, displayName)
        end

        -- 我的号码不显示物品名称

        DebugPrint("5倍彩钻生成完成，中奖号码: " .. table.concat(ticketData.winningNumbers, ","))
        DebugPrint("5倍彩钻我的号码: " .. table.concat(ticketData.myNumbers, ","))
        DebugPrint("5倍彩钻钻石图案位置: " .. table.concat(ticketData.diamondPositions, ","))

        -- 计算匹配数量
        local matchCount = 0
        for _, myNum in ipairs(ticketData.myNumbers) do
            for _, winNum in ipairs(ticketData.winningNumbers) do
                if myNum == winNum then
                    matchCount = matchCount + 1
                end
            end
        end
        DebugPrint("5倍彩钻匹配数量: " .. matchCount)
    elseif cardType == "scratch_zhongguofu" then
        -- 中国福：中奖号码 + 我的号码 + 福符号图案（逻辑与5倍彩钻相同，但只有15个中奖号码）
        ticketData.winningNumbers = {}
        ticketData.myNumbers = {}
        ticketData.numberItems = {} -- 存储每个号码对应的物品
        ticketData.fuPositions = {} -- 存储福符号图案位置

        -- 先生成2个我的号码
        for i = 1, 2 do
            table.insert(ticketData.myNumbers, math.random(1, 50))
            table.insert(ticketData.numberItems, "") -- 初始化物品为空
        end

        -- 生成15个中奖号码（先填充为非我的号码的随机数）
        for i = 1, 15 do
            local randomNumber
            repeat
                randomNumber = math.random(1, 50)
                -- 确保不是我的号码
                local isMyNumber = false
                for _, myNum in ipairs(ticketData.myNumbers) do
                    if randomNumber == myNum then
                        isMyNumber = true
                        break
                    end
                end
            until not isMyNumber

            table.insert(ticketData.winningNumbers, randomNumber)
        end

        -- 从配置中获取中国福号码的概率参数
        local matchTarget = 0

        -- 使用新的配置方式: zhongguofuMatchRates
        if Config.ScratchSymbols and Config.ScratchSymbols.zhongguofuMatchRates then
            -- 计算总权重
            local totalWeight = 0
            local possibleCounts = {}

            for count, weight in pairs(Config.ScratchSymbols.zhongguofuMatchRates) do
                table.insert(possibleCounts, count)
                totalWeight = totalWeight + weight
            end

            -- 对可能的匹配数量进行排序，确保顺序一致
            table.sort(possibleCounts, function(a, b) return tonumber(a) < tonumber(b) end)

            -- 添加调试信息，显示所有可能的匹配数和权重
            DebugPrint("中国福可能的匹配数量和权重:")
            for _, count in ipairs(possibleCounts) do
                DebugPrint("匹配数: " .. count .. " 权重: " .. Config.ScratchSymbols.zhongguofuMatchRates[count])
            end

            -- 随机决定放置多少个中奖号码
            local randomWeight = math.random() * totalWeight
            DebugPrint("中国福随机权重: " .. randomWeight .. " / 总权重: " .. totalWeight)
            local cumulativeWeight = 0
            local foundMatch = false

            for _, count in ipairs(possibleCounts) do
                local oldCumulativeWeight = cumulativeWeight
                cumulativeWeight = cumulativeWeight + Config.ScratchSymbols.zhongguofuMatchRates[count]
                DebugPrint("检查匹配数 " .. count .. ": 累积权重范围 " .. oldCumulativeWeight .. " - " .. cumulativeWeight)
                if randomWeight <= cumulativeWeight then
                    matchTarget = tonumber(count)
                    DebugPrint("选择匹配数: " .. count .. " (随机值: " .. randomWeight .. " 在范围内)")
                    foundMatch = true
                    break
                end
            end

            -- 如果没有找到匹配，使用默认值
            if not foundMatch then
                DebugPrint("警告：没有找到匹配的概率区间，使用默认值")
                if #possibleCounts > 0 then
                    matchTarget = tonumber(possibleCounts[1])
                    DebugPrint("使用默认匹配数: " .. matchTarget)
                else
                    matchTarget = 0
                    DebugPrint("没有可用的匹配数，使用0")
                end
            end
        else
            -- 兼容旧配置，默认随机0-5个匹配
            matchTarget = math.random(0, 5)
            DebugPrint("中国福将随机放置 " .. matchTarget .. " 个中奖号码 (直接概率模式)")
        end

        local matchCount = 0

        -- 创建一个包含15个中奖号码位置的数组，用于随机选择
        local positions = {}
        for i = 1, 15 do
            positions[i] = i
        end

        -- 随机打乱位置数组
        for i = #positions, 2, -1 do
            local j = math.random(i)
            positions[i], positions[j] = positions[j], positions[i]
        end

        -- 放置中奖号码，替换部分非中奖号码
        if matchTarget > 0 then -- 确保有中奖号码需要放置
            for i = 1, #positions do
                if matchCount >= matchTarget then
                    break  -- 已经放置了足够多的中奖号码
                end

                local pos = positions[i]
                -- 替换为中奖号码
                ticketData.winningNumbers[pos] = ticketData.myNumbers[math.random(#ticketData.myNumbers)]
                matchCount = matchCount + 1

                DebugPrint("在位置" .. pos .. "放置中国福中奖号码: " .. ticketData.winningNumbers[pos])
            end
        else
            DebugPrint("中国福本次不放置中奖号码")
        end

        -- 生成福符号图案位置
        local fuTarget = 0
        if Config.ScratchSymbols and Config.ScratchSymbols.zhongguofuFuRates then
            -- 计算总权重
            local totalWeight = 0
            local possibleCounts = {}

            for count, weight in pairs(Config.ScratchSymbols.zhongguofuFuRates) do
                table.insert(possibleCounts, count)
                totalWeight = totalWeight + weight
            end

            -- 对可能的福符号数量进行排序
            table.sort(possibleCounts, function(a, b) return tonumber(a) < tonumber(b) end)

            -- 随机决定放置多少个福符号
            local randomWeight = math.random() * totalWeight
            local cumulativeWeight = 0

            for _, count in ipairs(possibleCounts) do
                cumulativeWeight = cumulativeWeight + Config.ScratchSymbols.zhongguofuFuRates[count]
                if randomWeight <= cumulativeWeight then
                    fuTarget = tonumber(count)
                    break
                end
            end
        else
            -- 默认随机0-3个福符号
            fuTarget = math.random(0, 3)
        end

        -- 随机选择福符号位置（从15个中奖号码位置中选择）
        local fuPositions = {}
        for i = 1, 15 do
            fuPositions[i] = i
        end

        -- 随机打乱位置数组
        for i = #fuPositions, 2, -1 do
            local j = math.random(i)
            fuPositions[i], fuPositions[j] = fuPositions[j], fuPositions[i]
        end

        -- 放置福符号
        for i = 1, math.min(fuTarget, 15) do
            table.insert(ticketData.fuPositions, fuPositions[i])
        end

        DebugPrint("中国福福符号位置: " .. table.concat(ticketData.fuPositions, ","))

        -- 获取物品配置
        local cardConfig = Config.ScratchCards["scratch_zhongguofu"]

        -- 我的号码不需要生成物品，因为奖励来自匹配的中奖号码

        -- 为中奖号码生成物品（用于显示）
        ticketData.winningNumberItems = {}
        ticketData.winningNumberItemsDisplay = {} -- 用于显示的中文名称

        -- 获取所有可能的物品列表（用于非匹配时随机显示）
        local allItems = {}
        if cardConfig and cardConfig.winningItemRates then
            for itm, _ in pairs(cardConfig.winningItemRates) do
                table.insert(allItems, itm)
            end
        else
            -- 默认物品列表
            allItems = {"bread", "water", "bandage", "phone", "lockpick", "diamond", "gold", "weapon_pistol", "money", "car_key"}
        end

        -- 为每个中奖号码生成物品
        for i = 1, 15 do
            local winNum = ticketData.winningNumbers[i]
            local item = ""

            -- 检查是否是福符号位置
            local isFuPosition = false
            for _, fuPos in ipairs(ticketData.fuPositions) do
                if i == fuPos then
                    isFuPosition = true
                    break
                end
            end

            -- 检查是否匹配我的号码或者是福符号位置
            local isMatching = false
            for _, myNum in ipairs(ticketData.myNumbers) do
                if winNum == myNum or isFuPosition then
                    isMatching = true
                    break
                end
            end

            if isMatching then
                -- 匹配时：根据权重配置选择物品
                if cardConfig and cardConfig.winningItemRates then
                    local itemValues = {}
                    local itemWeights = {}
                    local totalWeight = 0

                    -- 收集所有物品和对应权重
                    for itm, weight in pairs(cardConfig.winningItemRates) do
                        table.insert(itemValues, itm)
                        table.insert(itemWeights, weight)
                        totalWeight = totalWeight + weight
                    end

                    -- 按权重随机选择物品
                    local r = math.random() * totalWeight
                    local cumulativeWeight = 0

                    for j = 1, #itemValues do
                        cumulativeWeight = cumulativeWeight + itemWeights[j]
                        if r <= cumulativeWeight then
                            item = itemValues[j]
                            break
                        end
                    end

                    if isFuPosition then
                        DebugPrint("中奖号码位置" .. i .. " 福符号匹配，选择物品:" .. item)
                    else
                        DebugPrint("中奖号码位置" .. i .. " 号码" .. winNum .. "匹配，选择物品:" .. item)
                    end
                else
                    item = "bread"
                    if isFuPosition then
                        DebugPrint("中奖号码位置" .. i .. " 福符号匹配，使用默认物品:" .. item)
                    else
                        DebugPrint("中奖号码位置" .. i .. " 号码" .. winNum .. "匹配，使用默认物品:" .. item)
                    end
                end
            else
                -- 不匹配时：随机显示物品
                item = allItems[math.random(#allItems)]
                DebugPrint("中奖号码位置" .. i .. " 号码" .. winNum .. "不匹配，随机显示物品:" .. item)
            end

            table.insert(ticketData.winningNumberItems, item)

            -- 生成显示名称，如果是带数量的money则显示金额
            local displayName = GetItemDisplayName(item)
            if string.find(item, "money:") then
                local parts = {}
                for part in string.gmatch(item, "([^:]+)") do
                    table.insert(parts, part)
                end
                if #parts == 2 then
                    displayName = "现金 ¥" .. parts[2]
                end
            end
            table.insert(ticketData.winningNumberItemsDisplay, displayName)
        end

        -- 我的号码不显示物品名称

        DebugPrint("中国福生成完成，中奖号码: " .. table.concat(ticketData.winningNumbers, ","))
        DebugPrint("中国福我的号码: " .. table.concat(ticketData.myNumbers, ","))
        DebugPrint("中国福福符号位置: " .. table.concat(ticketData.fuPositions, ","))

        -- 计算匹配数量
        local finalMatchCount = 0
        for _, myNum in ipairs(ticketData.myNumbers) do
            for _, winNum in ipairs(ticketData.winningNumbers) do
                if myNum == winNum then
                    finalMatchCount = finalMatchCount + 1
                end
            end
        end
        DebugPrint("中国福匹配数量: " .. finalMatchCount)
    elseif cardType == "scratch_chengfeng" then
        -- 乘风破浪：4行5列图标，⛵和🌊符号，无我的号码
        ticketData.icons = {} -- 存储每个位置的图标类型
        ticketData.iconItems = {} -- 存储每个位置对应的物品
        ticketData.sailPositions = {} -- 存储⛵符号位置
        ticketData.tornadoPositions = {} -- 存储🌊符号位置

        -- 定义多样化的非中奖图标
        local normalIcons = {"🐚", "🏖️", "🌴", "⚓", "🦀", "🐠", "🌺", "☀️", "⭐", "🏄"}

        -- 初始化20个位置（4行5列）
        for i = 1, 20 do
            -- 随机选择一个普通图标
            local randomIcon = normalIcons[math.random(#normalIcons)]
            table.insert(ticketData.icons, randomIcon) -- 随机普通图标
            table.insert(ticketData.iconItems, "") -- 初始化物品为空
        end

        -- 从配置中获取⛵符号数量的概率参数
        local sailTarget = 0
        local sailRates = Config.ScratchSymbols.chengfengSailRates
        if sailRates then
            local totalWeight = 0
            for count, weight in pairs(sailRates) do
                totalWeight = totalWeight + weight
            end

            local randomValue = math.random() * totalWeight
            local currentWeight = 0

            for count, weight in pairs(sailRates) do
                currentWeight = currentWeight + weight
                if randomValue <= currentWeight then
                    sailTarget = count
                    break
                end
            end
        end
        DebugPrint("乘风破浪目标⛵符号数量: " .. sailTarget)

        -- 从配置中获取🌊符号数量的概率参数
        local tornadoTarget = 0
        local tornadoRates = Config.ScratchSymbols.chengfengTornadoRates
        if tornadoRates then
            local totalWeight = 0
            for count, weight in pairs(tornadoRates) do
                totalWeight = totalWeight + weight
            end

            local randomValue = math.random() * totalWeight
            local currentWeight = 0

            for count, weight in pairs(tornadoRates) do
                currentWeight = currentWeight + weight
                if randomValue <= currentWeight then
                    tornadoTarget = count
                    break
                end
            end
        end
        DebugPrint("乘风破浪目标🌊符号数量: " .. tornadoTarget)

        -- 创建位置数组并随机打乱
        local positions = {}
        for i = 1, 20 do
            positions[i] = i
        end

        -- 随机打乱位置数组 (Fisher-Yates 洗牌算法)
        for i = #positions, 2, -1 do
            local j = math.random(i)
            positions[i], positions[j] = positions[j], positions[i]
        end

        -- 分配⛵符号位置
        for i = 1, sailTarget do
            if i <= #positions then
                local pos = positions[i]
                ticketData.icons[pos] = "sail"
                table.insert(ticketData.sailPositions, pos)
                DebugPrint("乘风破浪⛵符号位置: " .. pos)
            end
        end

        -- 分配🌊符号位置（从剩余位置中选择）
        local usedPositions = {}
        for _, pos in ipairs(ticketData.sailPositions) do
            usedPositions[pos] = true
        end

        local availablePositions = {}
        for i = 1, 20 do
            if not usedPositions[i] then
                table.insert(availablePositions, i)
            end
        end

        -- 随机打乱可用位置
        for i = #availablePositions, 2, -1 do
            local j = math.random(i)
            availablePositions[i], availablePositions[j] = availablePositions[j], availablePositions[i]
        end

        for i = 1, math.min(tornadoTarget, #availablePositions) do
            local pos = availablePositions[i]
            ticketData.icons[pos] = "🌊"
            table.insert(ticketData.tornadoPositions, pos)
            DebugPrint("乘风破浪🌊符号位置: " .. pos)
        end

        -- 为剩余的普通位置重新分配随机图标
        local allUsedPositions = {}
        for _, pos in ipairs(ticketData.sailPositions) do
            allUsedPositions[pos] = true
        end
        for _, pos in ipairs(ticketData.tornadoPositions) do
            allUsedPositions[pos] = true
        end

        -- 为非中奖位置重新分配随机图标
        for i = 1, 20 do
            if not allUsedPositions[i] then
                local randomIcon = normalIcons[math.random(#normalIcons)]
                ticketData.icons[i] = randomIcon
            end
        end

        -- 为所有位置分配物品
        -- 中奖符号使用winningItemRates权重配置，非中奖符号随机显示物品
        local winningItemRates = cardConfig.winningItemRates
        local iconItemRates = cardConfig.iconItemRates

        -- 准备中奖物品权重配置
        local winningItems = {}
        local winningWeights = {}
        local winningTotalWeight = 0
        if winningItemRates then
            for item, weight in pairs(winningItemRates) do
                table.insert(winningItems, item)
                table.insert(winningWeights, weight)
                winningTotalWeight = winningTotalWeight + weight
            end
        end

        -- 准备随机物品列表（用于非中奖位置）
        local randomItems = {}
        if iconItemRates then
            for item, weight in pairs(iconItemRates) do
                table.insert(randomItems, item)
            end
        end

        -- 为每个位置分配物品
        for i = 1, 20 do
            local isWinningPosition = false

            -- 检查是否为中奖位置（⛵ 或 🌊 符号）
            for _, pos in ipairs(ticketData.sailPositions or {}) do
                if pos == i then
                    isWinningPosition = true
                    break
                end
            end
            if not isWinningPosition then
                for _, pos in ipairs(ticketData.tornadoPositions or {}) do
                    if pos == i then
                        isWinningPosition = true
                        break
                    end
                end
            end

            if isWinningPosition and #winningItems > 0 then
                -- 中奖位置：使用权重配置选择物品
                local randomValue = math.random() * winningTotalWeight
                local currentWeight = 0

                for j = 1, #winningItems do
                    currentWeight = currentWeight + winningWeights[j]
                    if randomValue <= currentWeight then
                        ticketData.iconItems[i] = winningItems[j]
                        break
                    end
                end

                DebugPrint("乘风破浪中奖位置 " .. i .. " 图标: " .. ticketData.icons[i] .. " 物品: " .. ticketData.iconItems[i] .. " (权重配置)")
            elseif #randomItems > 0 then
                -- 非中奖位置：随机选择物品
                local randomItem = randomItems[math.random(#randomItems)]
                ticketData.iconItems[i] = randomItem

                DebugPrint("乘风破浪非中奖位置 " .. i .. " 图标: " .. ticketData.icons[i] .. " 物品: " .. ticketData.iconItems[i] .. " (随机)")
            else
                -- 备用方案
                ticketData.iconItems[i] = "bread"
                DebugPrint("乘风破浪位置 " .. i .. " 使用备用物品: bread")
            end
        end

        -- 生成物品显示名称
        ticketData.iconItemsDisplay = {}
        for i = 1, 20 do
            local item = ticketData.iconItems[i]
            local displayName = item

            -- 处理money类型的特殊显示
            if string.find(item, "money:") then
                local amount = string.match(item, "money:(%d+)")
                if amount then
                    displayName = "¥" .. amount
                end
            elseif Config.Items and Config.Items.itemNames and Config.Items.itemNames[item] then
                displayName = Config.Items.itemNames[item]
            end

            ticketData.iconItemsDisplay[i] = displayName
        end

        DebugPrint("乘风破浪生成完成，⛵符号位置: " .. table.concat(ticketData.sailPositions, ","))
        DebugPrint("乘风破浪🌊符号位置: " .. table.concat(ticketData.tornadoPositions, ","))
    end

    return ticketData
end

-- 计算刮刮乐奖金
function CalculateScratchPrize(cardType, ticketData)
    local cardConfig = Config.ScratchCards[cardType]
    
    -- 喜相逢特殊规则处理
    if cardType == "scratch_xixiangfeng" and ticketData.areas then
        local totalPrize = 0
        
        -- 遍历所有区域，检查"喜"和"囍"图案
        for _, area in pairs(ticketData.areas) do
            if area.symbol == "喜" then
                -- 喜图案：获得对应金额
                totalPrize = totalPrize + area.amount
            elseif area.symbol == "囍" then
                -- 囍图案：获得双倍金额
                totalPrize = totalPrize + (area.amount * 2)
            end
        end
        
        return totalPrize
    -- 福鼠送彩特殊规则处理
    elseif cardType == "scratch_fusong" and ticketData.winningNumber and ticketData.myNumbers then
        -- 获取行奖金配置，使用保存在票据数据中的行金额
        local rowPrizes = ticketData.rowAmounts or cardConfig.rowPrizes or {10, 20, 50, 100, 500}
        local totalPrize = 0
        
        -- 确保winningNumber是数字
        local winningNumber = tonumber(ticketData.winningNumber)
        
        -- 调试信息
        DebugPrint("福鼠送彩计算中奖 - 福鼠号码: " .. winningNumber)
        DebugPrint("福鼠送彩行金额: " .. table.concat(rowPrizes, ", "))
        DebugPrint("福鼠送彩号码矩阵:")
        for row = 1, 5 do
            local rowText = "第" .. row .. "行: "
            for col = 1, 5 do
                local index = (row - 1) * 5 + col
                if index <= #ticketData.myNumbers then
                    rowText = rowText .. ticketData.myNumbers[index] .. " "
                end
            end
            DebugPrint("" .. rowText)
        end
        
        -- 检查我的号码是否包含中奖号码，每个匹配的号码都计算一次奖金
        for i = 1, #ticketData.myNumbers do
            local myNumber = tonumber(ticketData.myNumbers[i])
            
            -- 检查号码是否匹配（确保都是数字类型）
            if myNumber == winningNumber then
                -- 计算所在行数（每行5个数字）
                local row = math.ceil(i / 5)
                local col = ((i-1) % 5) + 1
                if row >= 1 and row <= #rowPrizes then
                    -- 每个匹配的号码都加上对应行的奖金
                    totalPrize = totalPrize + rowPrizes[row]
                    DebugPrint("第" .. row .. "行第" .. col .. "列号码匹配 [" .. myNumber .. " = " .. winningNumber .. "]，奖金: " .. rowPrizes[row] .. "，累计奖金: " .. totalPrize)
                end
            end
        end
        
        DebugPrint("福鼠送彩最终奖金: " .. totalPrize)
        return totalPrize
    -- 耀出彩特殊规则处理
    elseif cardType == "scratch_yaocai" and ticketData.winningNumbers and ticketData.myNumbers then
        local totalPrize = 0
        local matchedIndices = {} -- 跟踪匹配的索引
        
        -- 调试信息
        DebugPrint("耀出彩计算中奖 - 中奖号码: " .. table.concat(ticketData.winningNumbers, ","))
        
        -- 查找匹配的号码位置
        for i, myNum in ipairs(ticketData.myNumbers) do
            for _, winNum in ipairs(ticketData.winningNumbers) do
                -- 确保类型一致，转换为数字进行比较
                local myNumber = tonumber(myNum)
                local winNumber = tonumber(winNum)
                if myNumber == winNumber then
                    table.insert(matchedIndices, i)
                    -- 使用实际金额，如果有的话
                    local amount = 0
                    if ticketData.numberAmounts and ticketData.numberAmounts[i] then
                        amount = tonumber(ticketData.numberAmounts[i]) or 0
                    else
                        -- 使用固定奖励表作为后备，保持兼容性
                        amount = 50 -- 默认金额
                    end
                    
                    totalPrize = totalPrize + amount
                    DebugPrint("耀出彩号码 " .. winNum .. " 在位置 " .. i .. " 匹配，金额: " .. amount .. "，累计奖金: " .. totalPrize)
                    break
                end
            end
        end
        
        DebugPrint("耀出彩最终奖金: " .. totalPrize)
        return totalPrize
    -- 5倍彩钻特殊规则处理
    elseif cardType == "scratch_caizuan" and ticketData.winningNumbers and ticketData.myNumbers then
        local totalItems = {} -- 存储所有获得的物品
        local matchedIndices = {} -- 跟踪匹配的索引

        -- 调试信息
        DebugPrint("5倍彩钻计算中奖 - 中奖号码: " .. table.concat(ticketData.winningNumbers, ","))
        DebugPrint("5倍彩钻我的号码: " .. table.concat(ticketData.myNumbers, ","))

        -- 查找匹配的号码位置和钻石图案位置
        for j, winNum in ipairs(ticketData.winningNumbers) do
            local isMatched = false

            -- 首先检查是否是钻石图案位置
            local isDiamondPosition = false
            if ticketData.diamondPositions then
                for _, pos in ipairs(ticketData.diamondPositions) do
                    if pos == j then
                        isDiamondPosition = true
                        isMatched = true -- 钻石图案自动算匹配
                        break
                    end
                end
            end

            -- 如果不是钻石图案，检查号码是否匹配
            if not isDiamondPosition then
                for i, myNum in ipairs(ticketData.myNumbers) do
                    -- 确保类型一致，转换为数字进行比较
                    local myNumber = tonumber(myNum)
                    local winNumber = tonumber(winNum)
                    if myNumber == winNumber then
                        isMatched = true
                        break
                    end
                end
            end

            -- 如果匹配（号码匹配或钻石图案），获得奖励
            if isMatched then
                table.insert(matchedIndices, {winIndex = j})

                -- 获取对应的物品（从中奖号码位置获取）
                local item = ""
                if ticketData.winningNumberItems and ticketData.winningNumberItems[j] then
                    item = ticketData.winningNumberItems[j]
                else
                    item = "bread" -- 默认物品
                end

                if isDiamondPosition then
                    -- 钻石图案：获得5倍物品
                    for k = 1, 5 do
                        table.insert(totalItems, item)
                    end
                    DebugPrint("5倍彩钻中奖号码位置 " .. j .. " 钻石图案匹配，物品: " .. item .. " x5")
                else
                    -- 普通匹配：获得1倍物品
                    table.insert(totalItems, item)
                    DebugPrint("5倍彩钻中奖号码位置 " .. j .. " 号码 " .. winNum .. " 匹配，物品: " .. item .. " x1")
                end
            end
        end

        -- 将物品信息存储到票据数据中，供后续兑奖使用
        ticketData.rewardItems = totalItems

        DebugPrint("5倍彩钻最终获得物品: " .. table.concat(totalItems, ", "))
        return #totalItems -- 返回物品数量作为"奖金"，实际奖励是物品
    -- 中国福特殊规则处理
    elseif cardType == "scratch_zhongguofu" and ticketData.winningNumbers and ticketData.myNumbers then
        local totalItems = {} -- 存储所有获得的物品
        local matchedIndices = {} -- 跟踪匹配的索引

        -- 调试信息
        DebugPrint("中国福计算中奖 - 中奖号码: " .. table.concat(ticketData.winningNumbers, ","))
        DebugPrint("中国福我的号码: " .. table.concat(ticketData.myNumbers, ","))

        -- 检查是否有福符号
        local hasFuSymbol = false
        if ticketData.fuPositions and #ticketData.fuPositions > 0 then
            hasFuSymbol = true
            DebugPrint("中国福检测到福符号，将获得所有中奖号码对应的物品")
        end

        -- 如果有福符号，获得所有中奖号码对应的物品
        if hasFuSymbol then
            for j, winNum in ipairs(ticketData.winningNumbers) do
                -- 获取对应的物品（从中奖号码位置获取）
                local item = ""
                if ticketData.winningNumberItems and ticketData.winningNumberItems[j] then
                    item = ticketData.winningNumberItems[j]
                else
                    item = "bread" -- 默认物品
                end

                -- 所有位置都获得1倍物品（包括福符号位置）
                table.insert(totalItems, item)
                DebugPrint("中国福中奖号码位置 " .. j .. " 号码/福符号 " .. winNum .. "，物品: " .. item .. " x1")
            end
        else
            -- 没有福符号，只有匹配的号码才获得奖励
            for j, winNum in ipairs(ticketData.winningNumbers) do
                local isMatched = false

                -- 检查号码是否匹配
                for i, myNum in ipairs(ticketData.myNumbers) do
                    -- 确保类型一致，转换为数字进行比较
                    local myNumber = tonumber(myNum)
                    local winNumber = tonumber(winNum)
                    if myNumber == winNumber then
                        isMatched = true
                        break
                    end
                end

                -- 如果匹配，获得奖励
                if isMatched then
                    table.insert(matchedIndices, {winIndex = j})

                    -- 获取对应的物品（从中奖号码位置获取）
                    local item = ""
                    if ticketData.winningNumberItems and ticketData.winningNumberItems[j] then
                        item = ticketData.winningNumberItems[j]
                    else
                        item = "bread" -- 默认物品
                    end

                    -- 普通匹配：获得1倍物品
                    table.insert(totalItems, item)
                    DebugPrint("中国福中奖号码位置 " .. j .. " 号码 " .. winNum .. " 匹配，物品: " .. item .. " x1")
                end
            end
        end

        -- 将物品信息存储到票据数据中，供后续兑奖使用
        ticketData.rewardItems = totalItems

        DebugPrint("中国福最终获得物品: " .. table.concat(totalItems, ", "))
        return #totalItems -- 返回物品数量作为"奖金"，实际奖励是物品
    elseif cardType == "scratch_chengfeng" and ticketData.icons and ticketData.iconItems then
        -- 乘风破浪特殊规则处理：⛵符号获得1倍物品，🌊符号获得2倍物品
        local totalItems = {}

        DebugPrint("乘风破浪开始计算奖品")
        DebugPrint("乘风破浪⛵符号位置: " .. table.concat(ticketData.sailPositions or {}, ", "))
        DebugPrint("乘风破浪🌊符号位置: " .. table.concat(ticketData.tornadoPositions or {}, ", "))

        -- 处理⛵符号位置（1倍物品）
        for _, pos in ipairs(ticketData.sailPositions or {}) do
            local item = ticketData.iconItems[pos]
            if item and item ~= "" then
                table.insert(totalItems, item)
                DebugPrint("乘风破浪⛵符号位置 " .. pos .. " 获得物品: " .. item .. " x1")
            end
        end

        -- 处理🌊符号位置（2倍物品）
        for _, pos in ipairs(ticketData.tornadoPositions or {}) do
            local item = ticketData.iconItems[pos]
            if item and item ~= "" then
                -- 🌊符号获得2倍物品
                table.insert(totalItems, item)
                table.insert(totalItems, item)
                DebugPrint("乘风破浪🌊符号位置 " .. pos .. " 获得物品: " .. item .. " x2")
            end
        end

        -- 将物品信息存储到票据数据中，供后续兑奖使用
        ticketData.rewardItems = totalItems

        DebugPrint("乘风破浪最终获得物品: " .. table.concat(totalItems, ", "))
        return #totalItems -- 返回物品数量作为"奖金"，实际奖励是物品
    end

    -- 其他类型刮刮乐的默认处理
    -- 注意：之前使用winRates配置，但该配置已被移除
    -- 现在使用固定奖励机制，所有刮刮乐都通过特定规则处理
    DebugPrint("警告：使用了默认处理逻辑，这种情况不应该发生")
    return 0 -- 默认不中奖
end

-- 获取物品中文名称
function GetItemDisplayName(itemName)
    if Config.Items and Config.Items.itemNames and Config.Items.itemNames[itemName] then
        return Config.Items.itemNames[itemName]
    end
    return itemName -- 如果没有配置，返回原始名称
end

-- 给予玩家物品 (框架兼容)
function GivePlayerItem(source, itemName, count, metadata)
    -- 检测是否使用ox_inventory
    if GetResourceState('ox_inventory') == 'started' then
        -- ox_inventory 方式
        DebugPrint("尝试使用ox_inventory给予物品: " .. itemName .. " x" .. count .. " 给玩家ID: " .. source)
        DebugPrint("元数据: " .. json.encode(metadata or {}))

        -- 检查物品是否存在
        local itemExists = pcall(function()
            return exports.ox_inventory:Items(itemName)
        end)

        if not itemExists then
            DebugPrint("警告: 物品 " .. itemName .. " 在ox_inventory中不存在！")
        end

        -- 尝试不同的ox_inventory调用方式
        local success, result = pcall(function()
            -- 方式1: 标准调用
            return exports.ox_inventory:AddItem(source, itemName, count, metadata)
        end)

        if success and result then
            DebugPrint("使用ox_inventory给予物品成功: " .. itemName .. " x" .. count .. " 给玩家ID: " .. source)
            return true
        else
            DebugPrint("ox_inventory标准调用失败，尝试其他方式...")

            -- 方式2: 尝试不带元数据
            local success2, result2 = pcall(function()
                return exports.ox_inventory:AddItem(source, itemName, count)
            end)

            if success2 and result2 then
                DebugPrint("使用ox_inventory给予物品成功(无元数据): " .. itemName .. " x" .. count .. " 给玩家ID: " .. source)
                return true
            else
                DebugPrint("使用ox_inventory给予物品失败: " .. itemName .. " x" .. count .. " 给玩家ID: " .. source)
                if not success then
                    DebugPrint("错误信息1: " .. tostring(result))
                end
                if not success2 then
                    DebugPrint("错误信息2: " .. tostring(result2))
                end
                -- 如果ox_inventory失败，尝试使用传统方式
            end
        end
    end

    -- 传统框架方式
    if Config.Framework == 'ESX' then
        local xPlayer = ESX.GetPlayerFromId(source)
        if xPlayer then
            xPlayer.addInventoryItem(itemName, count, metadata)
            DebugPrint("使用ESX给予物品: " .. itemName .. " x" .. count .. " 给玩家ID: " .. source)
            return true
        end
    elseif Config.Framework == 'QB' then
        local Player = QBCore.Functions.GetPlayer(source)
        if Player then
            Player.Functions.AddItem(itemName, count, false, metadata)
            DebugPrint("使用QB给予物品: " .. itemName .. " x" .. count .. " 给玩家ID: " .. source)
            return true
        end
    end
    
    return false
end

-- 测试物品给予的管理命令
RegisterCommand('test_scratch_item', function(source, args, rawCommand)
    if source == 0 then
        print("此命令只能在游戏中使用")
        return
    end

    local itemName = args[1] or "scratch_card_xixiangfeng"
    local count = tonumber(args[2]) or 1

    print("测试给予物品: " .. itemName .. " x" .. count .. " 给玩家ID: " .. source)

    local success = GivePlayerItem(source, itemName, count, {
        ticketId = 999,
        label = "测试刮刮乐",
        description = "测试用刮刮乐票据",
        card_type = "scratch_xixiangfeng"
    })

    if success then
        print("物品给予成功")
    else
        print("物品给予失败")
    end
end, false)

-- 移除玩家物品
function RemovePlayerItem(source, itemName, count)
    -- 检测是否使用ox_inventory
    if GetResourceState('ox_inventory') == 'started' then
        -- ox_inventory 方式
        exports.ox_inventory:RemoveItem(source, itemName, count)
        return
    end

    -- 传统框架方式
    if Config.Framework == 'ESX' then
        local xPlayer = ESX.GetPlayerFromId(source)
        if xPlayer then
            xPlayer.removeInventoryItem(itemName, count)
        end
    elseif Config.Framework == 'QB' then
        local Player = QBCore.Functions.GetPlayer(source)
        if Player then
            Player.Functions.RemoveItem(itemName, count)
        end
    end
end

-- 添加一个命令用于手动使用刮刮乐
RegisterCommand('usescratch', function(source, args, rawCommand)
    if source > 0 then
        local cardType = args[1]
        
        -- 检查卡片类型是否有效
        if not cardType or not Config.ScratchCards[cardType] then
            local validTypes = ""
            for k, _ in pairs(Config.ScratchCards) do
                validTypes = validTypes .. k .. ", "
            end
            SendNotification(source, "无效的刮刮乐类型。有效类型: " .. validTypes:sub(1, -3), "error")
            return
        end
        
        DebugPrint("玩家 " .. source .. " 使用命令使用刮刮乐: " .. cardType)
        
        -- 检查玩家是否有对应物品
        local hasItem = false
        local itemName = Config.Items.scratchItems[cardType]
        
        if GetResourceState('ox_inventory') == 'started' then
            local count = exports.ox_inventory:Search(source, 'count', itemName)
            if count and count > 0 then
                hasItem = true
                DebugPrint("玩家拥有物品 " .. itemName .. "，数量: " .. count)
            end
        elseif Config.Framework == 'ESX' then
        local xPlayer = ESX.GetPlayerFromId(source)
        if xPlayer and xPlayer.getInventoryItem(itemName) and xPlayer.getInventoryItem(itemName).count > 0 then
            hasItem = true
        end
    elseif Config.Framework == 'QB' then
            local Player = QBCore.Functions.GetPlayer(source)
            if Player and Player.Functions.GetItemByName(itemName) then
                hasItem = true
            end
        end
        
        if not hasItem then
            SendNotification(source, "你没有 " .. Config.ScratchCards[cardType].name .. " 刮刮乐", "error")
            return
        end
        
        -- 直接调用OpenScratchCardFromItem函数打开刮刮乐
        OpenScratchCardFromItem(source, cardType, nil)
    end
end, false)

-- 添加命令的帮助信息
TriggerEvent('chat:addSuggestion', '/usescratch', '使用刮刮乐', {
    { name = "类型", help = "刮刮乐类型 (scratch_xixiangfeng, scratch_fusong, scratch_yaocai, scratch_caizuan)" }
})

function ProcessScratchCardWin(source, player, cardId, prizeAmount)
    -- 如果奖金大于0，发放奖金
    if prizeAmount > 0 then
        -- 玩家抽中奖金
        Inventory.AddItem(source, "money", prizeAmount)
        
        -- 更新彩票店账户总支出
        MySQL.Async.execute(
            'UPDATE lottery_shop_accounts SET total_payout = total_payout + ? WHERE id = 1',
            {prizeAmount}
        )
        SystemPrint(string.format("^2[彩票系统] ^7已更新彩票店账户总支出，增加 %d (刮刮乐直接兑奖)", prizeAmount))
    end
end